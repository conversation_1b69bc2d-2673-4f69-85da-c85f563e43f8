using System;
using System.Collections.Generic;
using System.Linq;
using StudentGradesTracker.Models;
using StudentGradesTracker.Services;

namespace StudentGradesTracker.UI
{
    /// <summary>
    /// واجهة المستخدم في وحدة التحكم
    /// </summary>
    public class ConsoleUI
    {
        private StudentManager studentManager;
        private GradeManager gradeManager;
        private DataManager dataManager;
        private ReportGenerator reportGenerator;

        public ConsoleUI()
        {
            studentManager = new StudentManager();
            gradeManager = new GradeManager(studentManager);
            dataManager = new DataManager();
            reportGenerator = new ReportGenerator(studentManager, gradeManager);
        }

        public ConsoleUI(StudentManager studentManager, GradeManager gradeManager)
        {
            this.studentManager = studentManager;
            this.gradeManager = gradeManager;
            dataManager = new DataManager();
            reportGenerator = new ReportGenerator(studentManager, gradeManager);
        }

        /// <summary>
        /// تشغيل التطبيق الرئيسي
        /// </summary>
        public void Run()
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("=== نظام رصد درجات الطلاب - مادة تقنية المعلومات ===");
            Console.WriteLine();

            while (true)
            {
                ShowMainMenu();
                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        ManageStudents();
                        break;
                    case "2":
                        ManageGrades();
                        break;
                    case "3":
                        ShowReports();
                        break;
                    case "4":
                        ShowStatistics();
                        break;
                    case "5":
                        ManageData();
                        break;
                    case "6":
                        GenerateReports();
                        break;
                    case "0":
                        Console.WriteLine("شكراً لاستخدام النظام!");
                        return;
                    default:
                        Console.WriteLine("خيار غير صحيح، يرجى المحاولة مرة أخرى.");
                        break;
                }

                Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                Console.ReadKey();
                Console.Clear();
            }
        }

        /// <summary>
        /// عرض القائمة الرئيسية
        /// </summary>
        private void ShowMainMenu()
        {
            Console.WriteLine("=== القائمة الرئيسية ===");
            Console.WriteLine("1. إدارة الطلاب");
            Console.WriteLine("2. إدارة الدرجات");
            Console.WriteLine("3. التقارير");
            Console.WriteLine("4. الإحصائيات");
            Console.WriteLine("5. إدارة البيانات");
            Console.WriteLine("6. إنتاج التقارير");
            Console.WriteLine("0. خروج");
            Console.Write("اختر من القائمة: ");
        }

        /// <summary>
        /// إدارة الطلاب
        /// </summary>
        private void ManageStudents()
        {
            Console.Clear();
            Console.WriteLine("=== إدارة الطلاب ===");
            Console.WriteLine("1. إضافة طالب جديد");
            Console.WriteLine("2. عرض جميع الطلاب");
            Console.WriteLine("3. البحث عن طالب");
            Console.WriteLine("4. تعديل بيانات طالب");
            Console.WriteLine("5. حذف طالب");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    AddStudent();
                    break;
                case "2":
                    ShowAllStudents();
                    break;
                case "3":
                    SearchStudent();
                    break;
                case "4":
                    UpdateStudent();
                    break;
                case "5":
                    DeleteStudent();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// إضافة طالب جديد
        /// </summary>
        private void AddStudent()
        {
            Console.Clear();
            Console.WriteLine("=== إضافة طالب جديد ===");

            Console.Write("اسم الطالب: ");
            var name = Console.ReadLine();

            Console.Write("رقم الطالب: ");
            var studentNumber = Console.ReadLine();

            Console.Write("البريد الإلكتروني: ");
            var email = Console.ReadLine();

            try
            {
                var student = studentManager.AddStudent(name, studentNumber, email);
                Console.WriteLine($"تم إضافة الطالب بنجاح: {student}");
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض جميع الطلاب
        /// </summary>
        private void ShowAllStudents()
        {
            Console.Clear();
            Console.WriteLine("=== قائمة جميع الطلاب ===");

            var students = studentManager.GetAllStudents();
            if (students.Count == 0)
            {
                Console.WriteLine("لا يوجد طلاب مسجلين.");
                return;
            }

            Console.WriteLine($"{"المعرف",-5} {"الاسم",-20} {"رقم الطالب",-15} {"المعدل",-10}");
            Console.WriteLine(new string('-', 60));

            foreach (var student in students)
            {
                Console.WriteLine($"{student.Id,-5} {student.Name,-20} {student.StudentNumber,-15} {student.CalculateGPA(),-10:F2}");
            }

            Console.WriteLine($"\nإجمالي عدد الطلاب: {students.Count}");
        }

        /// <summary>
        /// البحث عن طالب
        /// </summary>
        private void SearchStudent()
        {
            Console.Clear();
            Console.WriteLine("=== البحث عن طالب ===");
            Console.WriteLine("1. البحث بالمعرف");
            Console.WriteLine("2. البحث برقم الطالب");
            Console.WriteLine("3. البحث بالاسم");
            Console.Write("اختر طريقة البحث: ");

            var choice = Console.ReadLine();
            Student student = null;
            List<Student> students = null;

            switch (choice)
            {
                case "1":
                    Console.Write("أدخل المعرف: ");
                    if (int.TryParse(Console.ReadLine(), out int id))
                    {
                        student = studentManager.GetStudentById(id);
                    }
                    break;
                case "2":
                    Console.Write("أدخل رقم الطالب: ");
                    var studentNumber = Console.ReadLine();
                    student = studentManager.GetStudentByNumber(studentNumber);
                    break;
                case "3":
                    Console.Write("أدخل الاسم أو جزء منه: ");
                    var name = Console.ReadLine();
                    students = studentManager.SearchStudentsByName(name);
                    break;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    return;
            }

            if (student != null)
            {
                Console.WriteLine($"\nتم العثور على الطالب: {student}");
                ShowStudentGrades(student.Id);
            }
            else if (students != null && students.Count > 0)
            {
                Console.WriteLine($"\nتم العثور على {students.Count} طالب:");
                foreach (var s in students)
                {
                    Console.WriteLine(s);
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على أي طالب.");
            }
        }

        /// <summary>
        /// تعديل بيانات طالب
        /// </summary>
        private void UpdateStudent()
        {
            Console.Clear();
            Console.WriteLine("=== تعديل بيانات طالب ===");

            Console.Write("أدخل معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int id))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var student = studentManager.GetStudentById(id);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            Console.WriteLine($"البيانات الحالية: {student}");
            Console.Write($"الاسم الجديد (الحالي: {student.Name}): ");
            var newName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(newName))
                newName = student.Name;

            Console.Write($"البريد الإلكتروني الجديد (الحالي: {student.Email}): ");
            var newEmail = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(newEmail))
                newEmail = student.Email;

            if (studentManager.UpdateStudent(id, newName, newEmail))
            {
                Console.WriteLine("تم تحديث بيانات الطالب بنجاح.");
            }
            else
            {
                Console.WriteLine("فشل في تحديث بيانات الطالب.");
            }
        }

        /// <summary>
        /// حذف طالب
        /// </summary>
        private void DeleteStudent()
        {
            Console.Clear();
            Console.WriteLine("=== حذف طالب ===");

            Console.Write("أدخل معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int id))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var student = studentManager.GetStudentById(id);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            Console.WriteLine($"هل أنت متأكد من حذف الطالب: {student}؟ (y/n)");
            var confirm = Console.ReadLine();

            if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
            {
                if (studentManager.DeleteStudent(id))
                {
                    Console.WriteLine("تم حذف الطالب بنجاح.");
                }
                else
                {
                    Console.WriteLine("فشل في حذف الطالب.");
                }
            }
            else
            {
                Console.WriteLine("تم إلغاء العملية.");
            }
        }

        /// <summary>
        /// إدارة الدرجات
        /// </summary>
        private void ManageGrades()
        {
            Console.Clear();
            Console.WriteLine("=== إدارة الدرجات ===");
            Console.WriteLine("1. إضافة درجة");
            Console.WriteLine("2. تعديل درجة");
            Console.WriteLine("3. حذف درجة");
            Console.WriteLine("4. عرض درجات طالب");
            Console.WriteLine("5. عرض درجات المادة");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    AddGrade();
                    break;
                case "2":
                    UpdateGrade();
                    break;
                case "3":
                    DeleteGrade();
                    break;
                case "4":
                    ShowStudentGradesMenu();
                    break;
                case "5":
                    ShowSubjectGrades();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// إضافة درجة
        /// </summary>
        private void AddGrade()
        {
            Console.Clear();
            Console.WriteLine("=== إضافة درجة ===");

            Console.Write("معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int studentId))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            Console.WriteLine($"الطالب: {student.Name}");
            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            Console.Write("الدرجة: ");
            if (!double.TryParse(Console.ReadLine(), out double score))
            {
                Console.WriteLine("درجة غير صحيحة.");
                return;
            }

            Console.Write("الدرجة القصوى (افتراضي: 100): ");
            var maxScoreInput = Console.ReadLine();
            double maxScore = 100.0;
            if (!string.IsNullOrWhiteSpace(maxScoreInput))
            {
                if (!double.TryParse(maxScoreInput, out maxScore))
                {
                    Console.WriteLine("درجة قصوى غير صحيحة، سيتم استخدام 100.");
                    maxScore = 100.0;
                }
            }

            Console.Write("الفصل الدراسي (اختياري): ");
            var semester = Console.ReadLine();

            if (gradeManager.AddGrade(studentId, subjectName, score, maxScore, semester))
            {
                Console.WriteLine("تم إضافة الدرجة بنجاح.");
            }
            else
            {
                Console.WriteLine("فشل في إضافة الدرجة.");
            }
        }

        /// <summary>
        /// تعديل درجة
        /// </summary>
        private void UpdateGrade()
        {
            Console.Clear();
            Console.WriteLine("=== تعديل درجة ===");

            Console.Write("معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int studentId))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            Console.WriteLine($"الطالب: {student.Name}");
            Console.Write("اسم المادة: ");
            var subjectName = Console.ReadLine();

            var existingGrade = student.GetGradeBySubject(subjectName);
            if (existingGrade == null)
            {
                Console.WriteLine("الطالب ليس لديه درجة في هذه المادة.");
                return;
            }

            Console.WriteLine($"الدرجة الحالية: {existingGrade}");
            Console.Write("الدرجة الجديدة: ");
            if (!double.TryParse(Console.ReadLine(), out double newScore))
            {
                Console.WriteLine("درجة غير صحيحة.");
                return;
            }

            if (gradeManager.UpdateGrade(studentId, subjectName, newScore))
            {
                Console.WriteLine("تم تعديل الدرجة بنجاح.");
            }
            else
            {
                Console.WriteLine("فشل في تعديل الدرجة.");
            }
        }

        /// <summary>
        /// حذف درجة
        /// </summary>
        private void DeleteGrade()
        {
            Console.Clear();
            Console.WriteLine("=== حذف درجة ===");

            Console.Write("معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int studentId))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            Console.WriteLine($"الطالب: {student.Name}");
            Console.Write("اسم المادة: ");
            var subjectName = Console.ReadLine();

            var existingGrade = student.GetGradeBySubject(subjectName);
            if (existingGrade == null)
            {
                Console.WriteLine("الطالب ليس لديه درجة في هذه المادة.");
                return;
            }

            Console.WriteLine($"هل أنت متأكد من حذف الدرجة: {existingGrade}؟ (y/n)");
            var confirm = Console.ReadLine();

            if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
            {
                if (gradeManager.DeleteGrade(studentId, subjectName))
                {
                    Console.WriteLine("تم حذف الدرجة بنجاح.");
                }
                else
                {
                    Console.WriteLine("فشل في حذف الدرجة.");
                }
            }
            else
            {
                Console.WriteLine("تم إلغاء العملية.");
            }
        }

        /// <summary>
        /// عرض درجات طالب - قائمة
        /// </summary>
        private void ShowStudentGradesMenu()
        {
            Console.Clear();
            Console.WriteLine("=== عرض درجات طالب ===");

            Console.Write("معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int studentId))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            ShowStudentGrades(studentId);
        }

        /// <summary>
        /// عرض درجات طالب
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        private void ShowStudentGrades(int studentId)
        {
            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                Console.WriteLine("الطالب غير موجود.");
                return;
            }

            var grades = gradeManager.GetStudentGrades(studentId);
            if (grades.Count == 0)
            {
                Console.WriteLine($"لا توجد درجات للطالب: {student.Name}");
                return;
            }

            Console.WriteLine($"\nدرجات الطالب: {student.Name} (رقم: {student.StudentNumber})");
            Console.WriteLine(new string('-', 80));
            Console.WriteLine($"{"المادة",-20} {"الدرجة",-10} {"النسبة",-10} {"الحرف",-8} {"النقاط",-8} {"التاريخ",-15}");
            Console.WriteLine(new string('-', 80));

            foreach (var grade in grades)
            {
                Console.WriteLine($"{grade.SubjectName,-20} {grade.Score}/{grade.MaxScore,-10} {grade.GetPercentage(),-10:F1}% {grade.LetterGrade,-8} {grade.Points,-8:F1} {grade.DateRecorded,-15:yyyy-MM-dd}");
            }

            Console.WriteLine(new string('-', 80));
            Console.WriteLine($"المعدل العام: {student.CalculateGPA():F2}");
        }

        /// <summary>
        /// عرض درجات المادة
        /// </summary>
        private void ShowSubjectGrades()
        {
            Console.Clear();
            Console.WriteLine("=== عرض درجات المادة ===");

            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var grades = gradeManager.GetSubjectGrades(subjectName);
            if (grades.Count == 0)
            {
                Console.WriteLine($"لا توجد درجات في مادة: {subjectName}");
                return;
            }

            Console.WriteLine($"\nدرجات مادة: {subjectName}");
            Console.WriteLine(new string('-', 80));
            Console.WriteLine($"{"معرف الطالب",-12} {"الدرجة",-10} {"النسبة",-10} {"الحرف",-8} {"النقاط",-8} {"التاريخ",-15}");
            Console.WriteLine(new string('-', 80));

            foreach (var grade in grades.OrderByDescending(g => g.GetPercentage()))
            {
                var student = studentManager.GetStudentById(grade.StudentId);
                var studentName = student?.Name ?? "غير معروف";
                Console.WriteLine($"{grade.StudentId,-12} {grade.Score}/{grade.MaxScore,-10} {grade.GetPercentage(),-10:F1}% {grade.LetterGrade,-8} {grade.Points,-8:F1} {grade.DateRecorded,-15:yyyy-MM-dd}");
            }

            Console.WriteLine(new string('-', 80));
            Console.WriteLine($"عدد الطلاب: {grades.Count}");
        }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        private void ShowReports()
        {
            Console.Clear();
            Console.WriteLine("=== التقارير ===");
            Console.WriteLine("1. تقرير أفضل الطلاب");
            Console.WriteLine("2. تقرير الطلاب حسب المعدل");
            Console.WriteLine("3. تقرير الطلاب الناجحين والراسبين");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    ShowTopStudentsReport();
                    break;
                case "2":
                    ShowStudentsByGPAReport();
                    break;
                case "3":
                    ShowPassFailReport();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// تقرير أفضل الطلاب
        /// </summary>
        private void ShowTopStudentsReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير أفضل الطلاب ===");

            var students = studentManager.GetStudentsSortedByGPA(true);
            if (students.Count == 0)
            {
                Console.WriteLine("لا يوجد طلاب مسجلين.");
                return;
            }

            Console.WriteLine($"{"الترتيب",-8} {"الاسم",-20} {"رقم الطالب",-15} {"المعدل",-10}");
            Console.WriteLine(new string('-', 60));

            for (int i = 0; i < Math.Min(10, students.Count); i++)
            {
                var student = students[i];
                Console.WriteLine($"{i + 1,-8} {student.Name,-20} {student.StudentNumber,-15} {student.CalculateGPA(),-10:F2}");
            }
        }

        /// <summary>
        /// تقرير الطلاب حسب المعدل
        /// </summary>
        private void ShowStudentsByGPAReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير الطلاب حسب المعدل ===");

            Console.Write("أدخل الحد الأدنى للمعدل: ");
            if (!double.TryParse(Console.ReadLine(), out double minGPA))
            {
                Console.WriteLine("معدل غير صحيح.");
                return;
            }

            var students = studentManager.GetStudentsWithGPAAbove(minGPA);
            if (students.Count == 0)
            {
                Console.WriteLine($"لا يوجد طلاب بمعدل أعلى من {minGPA:F2}");
                return;
            }

            Console.WriteLine($"\nالطلاب الذين لديهم معدل أعلى من {minGPA:F2}:");
            Console.WriteLine($"{"الاسم",-20} {"رقم الطالب",-15} {"المعدل",-10}");
            Console.WriteLine(new string('-', 50));

            foreach (var student in students.OrderByDescending(s => s.CalculateGPA()))
            {
                Console.WriteLine($"{student.Name,-20} {student.StudentNumber,-15} {student.CalculateGPA(),-10:F2}");
            }

            Console.WriteLine($"\nعدد الطلاب: {students.Count}");
        }

        /// <summary>
        /// تقرير الطلاب الناجحين والراسبين
        /// </summary>
        private void ShowPassFailReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير الطلاب الناجحين والراسبين ===");

            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var subject = gradeManager.GetSubjectStatistics(subjectName);
            if (subject == null || subject.Grades.Count == 0)
            {
                Console.WriteLine($"لا توجد درجات في مادة: {subjectName}");
                return;
            }

            var passingStudents = subject.Grades.Where(g => g.IsPassing()).ToList();
            var failingStudents = subject.Grades.Where(g => !g.IsPassing()).ToList();

            Console.WriteLine($"\nإحصائيات مادة: {subjectName}");
            Console.WriteLine(new string('=', 50));
            Console.WriteLine($"إجمالي الطلاب: {subject.Grades.Count}");
            Console.WriteLine($"الطلاب الناجحون: {passingStudents.Count}");
            Console.WriteLine($"الطلاب الراسبون: {failingStudents.Count}");
            Console.WriteLine($"نسبة النجاح: {subject.GetPassingRate():F1}%");

            if (passingStudents.Count > 0)
            {
                Console.WriteLine("\n--- الطلاب الناجحون ---");
                foreach (var grade in passingStudents.OrderByDescending(g => g.GetPercentage()))
                {
                    var student = studentManager.GetStudentById(grade.StudentId);
                    Console.WriteLine($"{student?.Name ?? "غير معروف"}: {grade.GetPercentage():F1}% ({grade.LetterGrade})");
                }
            }

            if (failingStudents.Count > 0)
            {
                Console.WriteLine("\n--- الطلاب الراسبون ---");
                foreach (var grade in failingStudents.OrderByDescending(g => g.GetPercentage()))
                {
                    var student = studentManager.GetStudentById(grade.StudentId);
                    Console.WriteLine($"{student?.Name ?? "غير معروف"}: {grade.GetPercentage():F1}% ({grade.LetterGrade})");
                }
            }
        }

        /// <summary>
        /// عرض الإحصائيات
        /// </summary>
        private void ShowStatistics()
        {
            Console.Clear();
            Console.WriteLine("=== الإحصائيات ===");
            Console.WriteLine("1. إحصائيات عامة");
            Console.WriteLine("2. إحصائيات المادة");
            Console.WriteLine("3. توزيع الدرجات");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    ShowGeneralStatistics();
                    break;
                case "2":
                    ShowSubjectStatistics();
                    break;
                case "3":
                    ShowGradeDistribution();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// إحصائيات عامة
        /// </summary>
        private void ShowGeneralStatistics()
        {
            Console.Clear();
            Console.WriteLine("=== الإحصائيات العامة ===");

            var students = studentManager.GetAllStudents();
            var subjects = gradeManager.GetAllSubjects();

            Console.WriteLine($"إجمالي عدد الطلاب: {students.Count}");
            Console.WriteLine($"إجمالي عدد المواد: {subjects.Count}");

            if (students.Count > 0)
            {
                var studentsWithGrades = students.Where(s => s.Grades.Count > 0).ToList();
                if (studentsWithGrades.Count > 0)
                {
                    var averageGPA = studentsWithGrades.Average(s => s.CalculateGPA());
                    var highestGPA = studentsWithGrades.Max(s => s.CalculateGPA());
                    var lowestGPA = studentsWithGrades.Min(s => s.CalculateGPA());

                    Console.WriteLine($"\nإحصائيات المعدلات:");
                    Console.WriteLine($"متوسط المعدل العام: {averageGPA:F2}");
                    Console.WriteLine($"أعلى معدل: {highestGPA:F2}");
                    Console.WriteLine($"أقل معدل: {lowestGPA:F2}");

                    var topStudent = studentsWithGrades.OrderByDescending(s => s.CalculateGPA()).First();
                    Console.WriteLine($"أفضل طالب: {topStudent.Name} (معدل: {topStudent.CalculateGPA():F2})");
                }
            }

            foreach (var subject in subjects)
            {
                if (subject.Grades.Count > 0)
                {
                    Console.WriteLine($"\nإحصائيات مادة {subject.Name}:");
                    Console.WriteLine($"عدد الطلاب: {subject.Grades.Count}");
                    Console.WriteLine($"متوسط الدرجات: {subject.CalculateAverageScore():F1}%");
                    Console.WriteLine($"نسبة النجاح: {subject.GetPassingRate():F1}%");
                }
            }
        }

        /// <summary>
        /// إحصائيات المادة
        /// </summary>
        private void ShowSubjectStatistics()
        {
            Console.Clear();
            Console.WriteLine("=== إحصائيات المادة ===");

            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var subject = gradeManager.GetSubjectStatistics(subjectName);
            if (subject == null || subject.Grades.Count == 0)
            {
                Console.WriteLine($"لا توجد درجات في مادة: {subjectName}");
                return;
            }

            Console.WriteLine($"\nإحصائيات مادة: {subject.Name}");
            Console.WriteLine(new string('=', 50));
            Console.WriteLine($"عدد الطلاب: {subject.Grades.Count}");
            Console.WriteLine($"متوسط الدرجات: {subject.CalculateAverageScore():F1}%");
            Console.WriteLine($"أعلى درجة: {subject.GetHighestScore():F1}%");
            Console.WriteLine($"أقل درجة: {subject.GetLowestScore():F1}%");
            Console.WriteLine($"عدد الناجحين: {subject.GetPassingStudentsCount()}");
            Console.WriteLine($"عدد الراسبين: {subject.GetFailingStudentsCount()}");
            Console.WriteLine($"نسبة النجاح: {subject.GetPassingRate():F1}%");
        }

        /// <summary>
        /// توزيع الدرجات
        /// </summary>
        private void ShowGradeDistribution()
        {
            Console.Clear();
            Console.WriteLine("=== توزيع الدرجات ===");

            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var subject = gradeManager.GetSubjectStatistics(subjectName);
            if (subject == null || subject.Grades.Count == 0)
            {
                Console.WriteLine($"لا توجد درجات في مادة: {subjectName}");
                return;
            }

            var distribution = subject.GetGradeDistribution();

            Console.WriteLine($"\nتوزيع الدرجات في مادة: {subject.Name}");
            Console.WriteLine(new string('=', 40));
            Console.WriteLine($"{"الدرجة الحرفية",-15} {"العدد",-8} {"النسبة",-10}");
            Console.WriteLine(new string('-', 40));

            foreach (var kvp in distribution)
            {
                double percentage = subject.Grades.Count > 0 ? (double)kvp.Value / subject.Grades.Count * 100 : 0;
                Console.WriteLine($"{kvp.Key,-15} {kvp.Value,-8} {percentage,-10:F1}%");
            }

            Console.WriteLine(new string('-', 40));
            Console.WriteLine($"{"المجموع",-15} {subject.Grades.Count,-8} {"100.0%",-10}");
        }

        /// <summary>
        /// إدارة البيانات
        /// </summary>
        private void ManageData()
        {
            Console.Clear();
            Console.WriteLine("=== إدارة البيانات ===");
            Console.WriteLine("1. حفظ البيانات");
            Console.WriteLine("2. تحميل البيانات");
            Console.WriteLine("3. إنشاء نسخة احتياطية");
            Console.WriteLine("4. تصدير البيانات إلى CSV");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    SaveData();
                    break;
                case "2":
                    LoadData();
                    break;
                case "3":
                    CreateBackup();
                    break;
                case "4":
                    ExportToCSV();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        private void SaveData()
        {
            Console.Clear();
            Console.WriteLine("=== حفظ البيانات ===");

            if (dataManager.SaveAllData(studentManager, gradeManager))
            {
                Console.WriteLine("تم حفظ البيانات بنجاح!");
            }
            else
            {
                Console.WriteLine("فشل في حفظ البيانات.");
            }
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            Console.Clear();
            Console.WriteLine("=== تحميل البيانات ===");

            Console.WriteLine("تحذير: سيتم استبدال البيانات الحالية بالبيانات المحفوظة.");
            Console.Write("هل أنت متأكد؟ (y/n): ");
            var confirm = Console.ReadLine();

            if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
            {
                // إنشاء مدراء جدد
                studentManager = new StudentManager();
                gradeManager = new GradeManager(studentManager);

                if (dataManager.LoadAllData(studentManager, gradeManager))
                {
                    Console.WriteLine("تم تحميل البيانات بنجاح!");
                }
                else
                {
                    Console.WriteLine("فشل في تحميل البيانات.");
                }
            }
            else
            {
                Console.WriteLine("تم إلغاء العملية.");
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private void CreateBackup()
        {
            Console.Clear();
            Console.WriteLine("=== إنشاء نسخة احتياطية ===");

            if (dataManager.CreateBackup())
            {
                Console.WriteLine("تم إنشاء النسخة الاحتياطية بنجاح!");
            }
            else
            {
                Console.WriteLine("فشل في إنشاء النسخة الاحتياطية.");
            }
        }

        /// <summary>
        /// تصدير البيانات إلى CSV
        /// </summary>
        private void ExportToCSV()
        {
            Console.Clear();
            Console.WriteLine("=== تصدير البيانات إلى CSV ===");

            Console.Write("اسم الملف (بدون امتداد): ");
            var fileName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(fileName))
            {
                fileName = $"students_export_{DateTime.Now:yyyyMMdd_HHmmss}";
            }

            fileName += ".csv";

            var students = studentManager.GetAllStudents();
            if (dataManager.ExportToCSV(students, fileName))
            {
                Console.WriteLine($"تم تصدير البيانات بنجاح إلى ملف: {fileName}");
            }
            else
            {
                Console.WriteLine("فشل في تصدير البيانات.");
            }
        }

        /// <summary>
        /// إنتاج التقارير
        /// </summary>
        private void GenerateReports()
        {
            Console.Clear();
            Console.WriteLine("=== إنتاج التقارير ===");
            Console.WriteLine("1. تقرير شامل عن الطلاب");
            Console.WriteLine("2. تقرير مادة معينة");
            Console.WriteLine("3. تقرير طالب معين");
            Console.WriteLine("4. تقرير أفضل الطلاب");
            Console.WriteLine("0. العودة للقائمة الرئيسية");
            Console.Write("اختر من القائمة: ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    GenerateStudentsReport();
                    break;
                case "2":
                    GenerateSubjectReport();
                    break;
                case "3":
                    GenerateStudentReport();
                    break;
                case "4":
                    GenerateTopStudentsReport();
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("خيار غير صحيح.");
                    break;
            }
        }

        /// <summary>
        /// إنتاج تقرير الطلاب
        /// </summary>
        private void GenerateStudentsReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير شامل عن الطلاب ===");

            var report = reportGenerator.GenerateStudentsReport();
            Console.WriteLine(report);

            Console.Write("\nهل تريد حفظ التقرير في ملف؟ (y/n): ");
            var save = Console.ReadLine();

            if (save?.ToLower() == "y" || save?.ToLower() == "yes")
            {
                var fileName = $"students_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                if (reportGenerator.SaveReportToFile(report, fileName))
                {
                    Console.WriteLine($"تم حفظ التقرير في ملف: Reports/{fileName}");
                }
                else
                {
                    Console.WriteLine("فشل في حفظ التقرير.");
                }
            }
        }

        /// <summary>
        /// إنتاج تقرير مادة
        /// </summary>
        private void GenerateSubjectReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير مادة ===");

            Console.Write("اسم المادة (افتراضي: تقنية المعلومات): ");
            var subjectName = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var report = reportGenerator.GenerateSubjectReport(subjectName);
            Console.WriteLine(report);

            Console.Write("\nهل تريد حفظ التقرير في ملف؟ (y/n): ");
            var save = Console.ReadLine();

            if (save?.ToLower() == "y" || save?.ToLower() == "yes")
            {
                var fileName = $"subject_report_{subjectName}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                if (reportGenerator.SaveReportToFile(report, fileName))
                {
                    Console.WriteLine($"تم حفظ التقرير في ملف: Reports/{fileName}");
                }
                else
                {
                    Console.WriteLine("فشل في حفظ التقرير.");
                }
            }
        }

        /// <summary>
        /// إنتاج تقرير طالب
        /// </summary>
        private void GenerateStudentReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير طالب ===");

            Console.Write("معرف الطالب: ");
            if (!int.TryParse(Console.ReadLine(), out int studentId))
            {
                Console.WriteLine("معرف غير صحيح.");
                return;
            }

            var report = reportGenerator.GenerateStudentReport(studentId);
            Console.WriteLine(report);

            Console.Write("\nهل تريد حفظ التقرير في ملف؟ (y/n): ");
            var save = Console.ReadLine();

            if (save?.ToLower() == "y" || save?.ToLower() == "yes")
            {
                var student = studentManager.GetStudentById(studentId);
                var studentName = student?.Name ?? "unknown";
                var fileName = $"student_report_{studentName}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                if (reportGenerator.SaveReportToFile(report, fileName))
                {
                    Console.WriteLine($"تم حفظ التقرير في ملف: Reports/{fileName}");
                }
                else
                {
                    Console.WriteLine("فشل في حفظ التقرير.");
                }
            }
        }

        /// <summary>
        /// إنتاج تقرير أفضل الطلاب
        /// </summary>
        private void GenerateTopStudentsReport()
        {
            Console.Clear();
            Console.WriteLine("=== تقرير أفضل الطلاب ===");

            Console.Write("عدد الطلاب المطلوب عرضهم (افتراضي: 10): ");
            var countInput = Console.ReadLine();
            int count = 10;
            if (!string.IsNullOrWhiteSpace(countInput))
            {
                if (!int.TryParse(countInput, out count) || count <= 0)
                {
                    count = 10;
                }
            }

            var report = reportGenerator.GenerateTopStudentsReport(count);
            Console.WriteLine(report);

            Console.Write("\nهل تريد حفظ التقرير في ملف؟ (y/n): ");
            var save = Console.ReadLine();

            if (save?.ToLower() == "y" || save?.ToLower() == "yes")
            {
                var fileName = $"top_students_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                if (reportGenerator.SaveReportToFile(report, fileName))
                {
                    Console.WriteLine($"تم حفظ التقرير في ملف: Reports/{fileName}");
                }
                else
                {
                    Console.WriteLine("فشل في حفظ التقرير.");
                }
            }
        }
    }
}
