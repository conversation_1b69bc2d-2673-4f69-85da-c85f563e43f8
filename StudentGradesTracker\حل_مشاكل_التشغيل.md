# 🛠️ دليل حل مشاكل تشغيل النظام

## 🔍 **تشخيص المشكلة**

### **الخطوة 1: تحديد نوع المشكلة**

#### ❌ **المشكلة الأكثر شيوعاً: عدم تثبيت .NET**
```
الخطأ: "لا يمكن العثور على dotnet" أو "dotnet غير معروف"
```

**الحل:**
1. 📥 تحميل .NET 6.0 من: https://dotnet.microsoft.com/download/dotnet/6.0
2. 🔄 إعادة تشغيل الكمبيوتر
3. ✅ تشغيل التطبيق مرة أخرى

#### ❌ **مشكلة: الملف التنفيذي لا يعمل**
```
الخطأ: النقر على .exe لا يحدث شيء أو يظهر خطأ
```

**الحل:**
1. 🖱️ انقر يمين على الملف → "تشغيل كمسؤول"
2. 🛡️ إضافة استثناء في Windows Defender
3. 🔧 استخدام `تشغيل_محسن.bat` بدلاً من ذلك

## 🚀 **الحلول المتدرجة**

### **الحل 1: التشغيل المباشر (الأسهل)**
```batch
🖱️ انقر مزدوج على: تشغيل_مباشر.bat
```

### **الحل 2: التشغيل المحسن (الأفضل)**
```batch
🖱️ انقر مزدوج على: تشغيل_محسن.bat
```

### **الحل 3: التشغيل اليدوي**
```cmd
1. فتح Command Prompt
2. الانتقال للمجلد: cd "C:\Users\<USER>\Desktop\computer_ma\StudentGradesTracker"
3. تشغيل الأمر: dotnet run
```

### **الحل 4: التشغيل من PowerShell**
```powershell
1. فتح PowerShell كمسؤول
2. تشغيل: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
3. الانتقال للمجلد: Set-Location "C:\Users\<USER>\Desktop\computer_ma\StudentGradesTracker"
4. تشغيل: dotnet run
```

## 🔧 **حلول المشاكل الشائعة**

### **مشكلة 1: "Access Denied" أو "تم رفض الوصول"**
**الحل:**
```
1. 🖱️ انقر يمين على الملف → "تشغيل كمسؤول"
2. أو انقر يمين على Command Prompt → "تشغيل كمسؤول"
```

### **مشكلة 2: "Windows Defender حجب الملف"**
**الحل:**
```
1. 🛡️ فتح Windows Security
2. ➕ Virus & threat protection → Manage settings
3. ➕ Add or remove exclusions → Add an exclusion
4. 📁 اختيار المجلد الكامل للتطبيق
```

### **مشكلة 3: "الأحرف العربية لا تظهر صحيحة"**
**الحل:**
```
1. 🖱️ انقر يمين على شريط عنوان Command Prompt
2. ⚙️ Properties → Font
3. 🔤 اختيار خط: "Consolas" أو "Courier New"
4. ✅ OK
```

### **مشكلة 4: "dotnet command not found"**
**الحل الكامل:**
```
1. 📥 تحميل .NET 6.0 SDK من:
   https://dotnet.microsoft.com/download/dotnet/6.0

2. 🔄 إعادة تشغيل الكمبيوتر

3. ✅ فتح Command Prompt جديد واختبار:
   dotnet --version

4. إذا لم يعمل، إضافة PATH يدوياً:
   - فتح System Properties → Environment Variables
   - إضافة: C:\Program Files\dotnet
```

### **مشكلة 5: "The application to execute does not exist"**
**الحل:**
```
1. 🔨 بناء المشروع مرة أخرى:
   dotnet clean
   dotnet restore
   dotnet build

2. 🚀 تشغيل التطبيق:
   dotnet run
```

## 📋 **قائمة فحص سريعة**

### ✅ **تأكد من:**
- [ ] تثبيت .NET 6.0 أو أحدث
- [ ] وجود ملف `StudentGradesTracker.csproj`
- [ ] عدم تشغيل نسخة أخرى من التطبيق
- [ ] وجود مساحة كافية على القرص الصلب (100 MB على الأقل)
- [ ] عدم حجب مكافح الفيروسات للملفات

### 🔍 **للتحقق من .NET:**
```cmd
dotnet --version
dotnet --list-runtimes
dotnet --list-sdks
```

## 🆘 **إذا لم تنجح جميع الحلول**

### **الحل الأخير: إعادة البناء الكامل**
```batch
1. 🗑️ حذف مجلدات bin و obj
2. 🔄 فتح Command Prompt كمسؤول
3. 📁 الانتقال لمجلد المشروع
4. 🧹 تشغيل: dotnet clean
5. 📦 تشغيل: dotnet restore
6. 🔨 تشغيل: dotnet build
7. 🚀 تشغيل: dotnet run
```

### **إنشاء ملف تنفيذي جديد:**
```batch
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

## 📞 **طلب المساعدة**

إذا استمرت المشكلة، يرجى تجميع المعلومات التالية:

### 📋 **معلومات النظام:**
```cmd
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
dotnet --version
dotnet --list-runtimes
```

### 📝 **رسالة الخطأ:**
- نسخ النص الكامل لرسالة الخطأ
- لقطة شاشة للخطأ
- الخطوات التي أدت للخطأ

---

## 🎯 **ملخص سريع للحلول**

| المشكلة | الحل السريع |
|---------|-------------|
| 🚫 .NET غير مثبت | تحميل من الموقع الرسمي |
| 🛡️ Windows Defender | إضافة استثناء |
| 🔐 Access Denied | تشغيل كمسؤول |
| 🔤 أحرف عربية خاطئة | تغيير خط وحدة التحكم |
| 📁 ملفات مفقودة | إعادة بناء المشروع |

**أسهل حل:** استخدم `تشغيل_محسن.bat` - يحل معظم المشاكل تلقائياً! 🎉
