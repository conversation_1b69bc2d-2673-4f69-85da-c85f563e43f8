# 🖥️ دليل تشغيل نظام رصد درجات الطلاب على ويندوز

## 🎯 الطرق المختلفة لتشغيل التطبيق

### 1. 🚀 **الطريقة الأسرع - الملف التنفيذي المستقل**

#### أ) تشغيل مباشر:
```
📁 انتقل إلى: bin\Release\net6.0\win-x64\publish\
🖱️ انقر مزدوج على: StudentGradesTracker.exe
```

#### ب) استخدام ملف Batch:
```
🖱️ انقر مزدوج على: تشغيل_النظام.bat
```

#### ج) استخدام PowerShell:
```
🖱️ انقر يمين على: تشغيل_النظام.ps1
📋 اختر: "تشغيل بـ PowerShell"
```

### 2. 🖥️ **إنشاء اختصار سطح المكتب**

```
🖱️ انقر مزدوج على: إنشاء_اختصار_سطح_المكتب.bat
✅ سيتم إنشاء اختصار على سطح المكتب تلقائياً
🖱️ انقر مزدوج على الاختصار لتشغيل النظام
```

### 3. 💻 **التشغيل من Command Prompt**

```cmd
cd "C:\Users\<USER>\Desktop\computer_ma\StudentGradesTracker"
dotnet run
```

### 4. 🔧 **التشغيل من PowerShell**

```powershell
Set-Location "C:\Users\<USER>\Desktop\computer_ma\StudentGradesTracker"
dotnet run
```

## 📋 **متطلبات التشغيل**

### للملف التنفيذي المستقل (.exe):
- ✅ **لا يحتاج .NET Runtime** (مدمج في الملف)
- ✅ **يعمل على أي جهاز ويندوز 10/11**
- ✅ **حجم أكبر لكن أسهل في التوزيع**

### للتشغيل بـ dotnet:
- 🔧 **يحتاج .NET 6.0 Runtime أو أحدث**
- 📥 **تحميل من**: https://dotnet.microsoft.com/download
- 💾 **حجم أصغر**

## 🎨 **مميزات الواجهة الاحترافية**

### 🌈 **الألوان والتصميم:**
- 🎨 واجهة ملونة احترافية
- 📊 جداول منسقة بحدود جميلة
- 🎯 رموز تعبيرية لكل خيار
- 📅 عرض التاريخ الميلادي

### 📱 **سهولة الاستخدام:**
- 🔢 قوائم مرقمة واضحة
- ✅ رسائل نجاح خضراء
- ❌ رسائل خطأ حمراء
- ℹ️ معلومات زرقاء
- ⚠️ تحذيرات صفراء

## 🗂️ **هيكل الملفات**

```
StudentGradesTracker/
├── 📁 bin/Release/net6.0/win-x64/publish/
│   └── 🎯 StudentGradesTracker.exe          # الملف التنفيذي الرئيسي
├── 📁 Data/                                 # بيانات النظام
│   ├── 💾 students.json                     # بيانات الطلاب
│   ├── 📚 subjects.json                     # بيانات المواد
│   └── 📁 Backup/                          # النسخ الاحتياطية
├── 📁 Reports/                             # التقارير المحفوظة
├── 🚀 تشغيل_النظام.bat                     # ملف تشغيل Batch
├── 🔧 تشغيل_النظام.ps1                     # ملف تشغيل PowerShell
├── 🖥️ إنشاء_اختصار_سطح_المكتب.bat         # إنشاء اختصار
└── 📖 دليل_التشغيل.md                      # هذا الملف
```

## 🔧 **استكشاف الأخطاء وحلها**

### ❌ **خطأ: "لا يمكن العثور على dotnet"**
**الحل:**
```
1. 📥 تحميل .NET 6.0 من الموقع الرسمي
2. 🔄 إعادة تشغيل الكمبيوتر
3. ✅ استخدام الملف التنفيذي المستقل بدلاً من ذلك
```

### ❌ **خطأ: "الملف محجوب بواسطة Windows Defender"**
**الحل:**
```
1. 🛡️ فتح Windows Defender
2. ➕ إضافة استثناء للمجلد
3. ✅ السماح بتشغيل التطبيق
```

### ❌ **خطأ: "لا تظهر الأحرف العربية بشكل صحيح"**
**الحل:**
```
1. ⚙️ فتح Command Prompt كمسؤول
2. 💻 تشغيل: chcp 65001
3. 🔤 تغيير خط وحدة التحكم إلى "Consolas" أو "Courier New"
```

## 🎯 **نصائح للاستخدام الأمثل**

### 🖥️ **لأفضل تجربة:**
1. 📺 **استخدم شاشة بدقة عالية** لعرض أفضل للجداول
2. 🎨 **فعّل الألوان في وحدة التحكم** (مفعلة افتراضياً في Windows 10/11)
3. 💾 **قم بعمل نسخ احتياطية دورية** من خلال النظام
4. 📊 **استخدم ميزة التقارير** لحفظ البيانات كملفات

### 🔄 **للتحديثات:**
1. 📥 **احتفظ بنسخة احتياطية** من مجلد Data
2. 🔄 **استبدل الملفات الجديدة** مع الاحتفاظ بمجلد Data
3. ✅ **اختبر النظام** بعد التحديث

## 📞 **الدعم الفني**

### 🆘 **في حالة وجود مشاكل:**
1. 📋 **تحقق من دليل استكشاف الأخطاء** أعلاه
2. 🔄 **جرب إعادة تشغيل النظام**
3. 💾 **تأكد من وجود مساحة كافية** على القرص الصلب
4. 🛡️ **تحقق من إعدادات مكافح الفيروسات**

---

## 🎉 **استمتع باستخدام النظام!**

النظام جاهز للاستخدام بجميع ميزاته الاحترافية. اختر الطريقة الأنسب لك من الطرق المذكورة أعلاه وابدأ في إدارة درجات الطلاب بكفاءة عالية!
