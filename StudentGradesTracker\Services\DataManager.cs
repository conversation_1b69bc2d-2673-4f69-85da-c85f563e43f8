using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using StudentGradesTracker.Models;

namespace StudentGradesTracker.Services
{
    /// <summary>
    /// فئة إدارة البيانات - حفظ واسترجاع
    /// </summary>
    public class DataManager
    {
        private const string DataDirectory = "Data";
        private const string StudentsFile = "students.json";
        private const string SubjectsFile = "subjects.json";

        public DataManager()
        {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            if (!Directory.Exists(DataDirectory))
            {
                Directory.CreateDirectory(DataDirectory);
            }
        }

        /// <summary>
        /// حفظ بيانات الطلاب
        /// </summary>
        /// <param name="students">قائمة الطلاب</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveStudents(List<Student> students)
        {
            try
            {
                var filePath = Path.Combine(DataDirectory, StudentsFile);
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(students, options);
                File.WriteAllText(filePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ بيانات الطلاب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل بيانات الطلاب
        /// </summary>
        /// <returns>قائمة الطلاب أو قائمة فارغة</returns>
        public List<Student> LoadStudents()
        {
            try
            {
                var filePath = Path.Combine(DataDirectory, StudentsFile);
                if (!File.Exists(filePath))
                {
                    return new List<Student>();
                }

                var json = File.ReadAllText(filePath, System.Text.Encoding.UTF8);
                var students = JsonSerializer.Deserialize<List<Student>>(json);
                return students ?? new List<Student>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل بيانات الطلاب: {ex.Message}");
                return new List<Student>();
            }
        }

        /// <summary>
        /// حفظ بيانات المواد
        /// </summary>
        /// <param name="subjects">قائمة المواد</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveSubjects(List<Subject> subjects)
        {
            try
            {
                var filePath = Path.Combine(DataDirectory, SubjectsFile);
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(subjects, options);
                File.WriteAllText(filePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ بيانات المواد: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل بيانات المواد
        /// </summary>
        /// <returns>قائمة المواد أو قائمة فارغة</returns>
        public List<Subject> LoadSubjects()
        {
            try
            {
                var filePath = Path.Combine(DataDirectory, SubjectsFile);
                if (!File.Exists(filePath))
                {
                    return new List<Subject>();
                }

                var json = File.ReadAllText(filePath, System.Text.Encoding.UTF8);
                var subjects = JsonSerializer.Deserialize<List<Subject>>(json);
                return subjects ?? new List<Subject>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل بيانات المواد: {ex.Message}");
                return new List<Subject>();
            }
        }

        /// <summary>
        /// حفظ جميع البيانات
        /// </summary>
        /// <param name="studentManager">مدير الطلاب</param>
        /// <param name="gradeManager">مدير الدرجات</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveAllData(StudentManager studentManager, GradeManager gradeManager)
        {
            try
            {
                var students = studentManager.GetAllStudents();
                var subjects = gradeManager.GetAllSubjects();

                bool studentsSuccess = SaveStudents(students);
                bool subjectsSuccess = SaveSubjects(subjects);

                return studentsSuccess && subjectsSuccess;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل جميع البيانات
        /// </summary>
        /// <param name="studentManager">مدير الطلاب</param>
        /// <param name="gradeManager">مدير الدرجات</param>
        /// <returns>true إذا تم التحميل بنجاح</returns>
        public bool LoadAllData(StudentManager studentManager, GradeManager gradeManager)
        {
            try
            {
                var students = LoadStudents();
                var subjects = LoadSubjects();

                // تحميل الطلاب
                foreach (var student in students)
                {
                    try
                    {
                        studentManager.AddStudent(student.Name, student.StudentNumber, student.Email);
                        var addedStudent = studentManager.GetStudentByNumber(student.StudentNumber);
                        if (addedStudent != null)
                        {
                            addedStudent.Id = student.Id;
                            addedStudent.EnrollmentDate = student.EnrollmentDate;
                            addedStudent.Grades = student.Grades ?? new List<Grade>();
                        }
                    }
                    catch (ArgumentException)
                    {
                        // تجاهل الطلاب المكررين
                        Console.WriteLine($"تم تجاهل الطالب المكرر: {student.StudentNumber}");
                    }
                }

                // تحميل المواد
                foreach (var subject in subjects)
                {
                    var existingSubject = gradeManager.GetSubjectByName(subject.Name);
                    if (existingSubject == null)
                    {
                        gradeManager.AddSubject(subject.Name, subject.Code, subject.CreditHours, subject.Instructor);
                        existingSubject = gradeManager.GetSubjectByName(subject.Name);
                    }
                    
                    if (existingSubject != null)
                    {
                        existingSubject.Id = subject.Id;
                        existingSubject.Description = subject.Description;
                        existingSubject.Semester = subject.Semester;
                        existingSubject.Year = subject.Year;
                        existingSubject.Grades = subject.Grades ?? new List<Grade>();
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من البيانات
        /// </summary>
        /// <returns>true إذا تم إنشاء النسخة الاحتياطية بنجاح</returns>
        public bool CreateBackup()
        {
            try
            {
                var backupDirectory = Path.Combine(DataDirectory, "Backup");
                if (!Directory.Exists(backupDirectory))
                {
                    Directory.CreateDirectory(backupDirectory);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupStudentsFile = Path.Combine(backupDirectory, $"students_backup_{timestamp}.json");
                var backupSubjectsFile = Path.Combine(backupDirectory, $"subjects_backup_{timestamp}.json");

                var studentsFile = Path.Combine(DataDirectory, StudentsFile);
                var subjectsFile = Path.Combine(DataDirectory, SubjectsFile);

                if (File.Exists(studentsFile))
                {
                    File.Copy(studentsFile, backupStudentsFile);
                }

                if (File.Exists(subjectsFile))
                {
                    File.Copy(subjectsFile, backupSubjectsFile);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى ملف CSV
        /// </summary>
        /// <param name="students">قائمة الطلاب</param>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>true إذا تم التصدير بنجاح</returns>
        public bool ExportToCSV(List<Student> students, string fileName)
        {
            try
            {
                var filePath = Path.Combine(DataDirectory, fileName);
                using (var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8))
                {
                    // كتابة العناوين
                    writer.WriteLine("معرف الطالب,اسم الطالب,رقم الطالب,البريد الإلكتروني,المعدل العام,عدد المواد");

                    // كتابة بيانات الطلاب
                    foreach (var student in students)
                    {
                        writer.WriteLine($"{student.Id},{student.Name},{student.StudentNumber},{student.Email},{student.CalculateGPA():F2},{student.Grades.Count}");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تصدير البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود ملفات البيانات
        /// </summary>
        /// <returns>true إذا كانت ملفات البيانات موجودة</returns>
        public bool DataFilesExist()
        {
            var studentsFile = Path.Combine(DataDirectory, StudentsFile);
            var subjectsFile = Path.Combine(DataDirectory, SubjectsFile);
            
            return File.Exists(studentsFile) || File.Exists(subjectsFile);
        }
    }
}
