using System;
using System.Collections.Generic;
using System.Linq;
using StudentGradesTracker.Models;

namespace StudentGradesTracker.Services
{
    /// <summary>
    /// فئة إدارة الطلاب
    /// </summary>
    public class StudentManager
    {
        private List<Student> students;
        private int nextId;

        public StudentManager()
        {
            students = new List<Student>();
            nextId = 1;
        }

        /// <summary>
        /// إضافة طالب جديد
        /// </summary>
        /// <param name="name">اسم الطالب</param>
        /// <param name="studentNumber">رقم الطالب</param>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>الطالب المضاف</returns>
        public Student AddStudent(string name, string studentNumber, string email)
        {
            // التحقق من عدم وجود رقم طالب مكرر
            if (students.Any(s => s.StudentNumber == studentNumber))
            {
                throw new ArgumentException("رقم الطالب موجود مسبقاً");
            }

            var student = new Student(nextId++, name, studentNumber, email);
            students.Add(student);
            return student;
        }

        /// <summary>
        /// البحث عن طالب بالمعرف
        /// </summary>
        /// <param name="id">معرف الطالب</param>
        /// <returns>الطالب أو null</returns>
        public Student GetStudentById(int id)
        {
            return students.FirstOrDefault(s => s.Id == id);
        }

        /// <summary>
        /// البحث عن طالب برقم الطالب
        /// </summary>
        /// <param name="studentNumber">رقم الطالب</param>
        /// <returns>الطالب أو null</returns>
        public Student GetStudentByNumber(string studentNumber)
        {
            return students.FirstOrDefault(s => s.StudentNumber == studentNumber);
        }

        /// <summary>
        /// البحث عن طلاب بالاسم
        /// </summary>
        /// <param name="name">اسم الطالب أو جزء منه</param>
        /// <returns>قائمة الطلاب المطابقين</returns>
        public List<Student> SearchStudentsByName(string name)
        {
            return students.Where(s => s.Name.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// الحصول على جميع الطلاب
        /// </summary>
        /// <returns>قائمة جميع الطلاب</returns>
        public List<Student> GetAllStudents()
        {
            return new List<Student>(students);
        }

        /// <summary>
        /// تحديث بيانات طالب
        /// </summary>
        /// <param name="id">معرف الطالب</param>
        /// <param name="name">الاسم الجديد</param>
        /// <param name="email">البريد الإلكتروني الجديد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateStudent(int id, string name, string email)
        {
            var student = GetStudentById(id);
            if (student != null)
            {
                student.Name = name;
                student.Email = email;
                return true;
            }
            return false;
        }

        /// <summary>
        /// حذف طالب
        /// </summary>
        /// <param name="id">معرف الطالب</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteStudent(int id)
        {
            var student = GetStudentById(id);
            if (student != null)
            {
                students.Remove(student);
                return true;
            }
            return false;
        }

        /// <summary>
        /// الحصول على عدد الطلاب
        /// </summary>
        /// <returns>عدد الطلاب</returns>
        public int GetStudentCount()
        {
            return students.Count;
        }

        /// <summary>
        /// الحصول على الطلاب مرتبين حسب المعدل
        /// </summary>
        /// <param name="descending">ترتيب تنازلي إذا كان true</param>
        /// <returns>قائمة الطلاب مرتبة</returns>
        public List<Student> GetStudentsSortedByGPA(bool descending = true)
        {
            if (descending)
                return students.OrderByDescending(s => s.CalculateGPA()).ToList();
            else
                return students.OrderBy(s => s.CalculateGPA()).ToList();
        }

        /// <summary>
        /// الحصول على الطلاب الذين لديهم معدل أعلى من قيمة معينة
        /// </summary>
        /// <param name="minGPA">الحد الأدنى للمعدل</param>
        /// <returns>قائمة الطلاب</returns>
        public List<Student> GetStudentsWithGPAAbove(double minGPA)
        {
            return students.Where(s => s.CalculateGPA() >= minGPA).ToList();
        }
    }
}
