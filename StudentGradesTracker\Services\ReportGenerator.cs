using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using StudentGradesTracker.Models;

namespace StudentGradesTracker.Services
{
    /// <summary>
    /// فئة إنتاج التقارير
    /// </summary>
    public class ReportGenerator
    {
        private StudentManager studentManager;
        private GradeManager gradeManager;

        public ReportGenerator(StudentManager studentManager, GradeManager gradeManager)
        {
            this.studentManager = studentManager;
            this.gradeManager = gradeManager;
        }

        /// <summary>
        /// إنتاج تقرير شامل عن جميع الطلاب
        /// </summary>
        /// <returns>نص التقرير</returns>
        public string GenerateStudentsReport()
        {
            var sb = new StringBuilder();
            var students = studentManager.GetAllStudents();

            sb.AppendLine("=== تقرير شامل عن الطلاب ===");
            sb.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"إجمالي عدد الطلاب: {students.Count}");
            sb.AppendLine();

            if (students.Count == 0)
            {
                sb.AppendLine("لا يوجد طلاب مسجلين في النظام.");
                return sb.ToString();
            }

            sb.AppendLine($"{"المعرف",-8} {"الاسم",-25} {"رقم الطالب",-15} {"البريد الإلكتروني",-30} {"المعدل",-8} {"عدد المواد",-10}");
            sb.AppendLine(new string('-', 100));

            foreach (var student in students.OrderBy(s => s.Name))
            {
                sb.AppendLine($"{student.Id,-8} {student.Name,-25} {student.StudentNumber,-15} {student.Email,-30} {student.CalculateGPA(),-8:F2} {student.Grades.Count,-10}");
            }

            sb.AppendLine();
            sb.AppendLine("=== إحصائيات عامة ===");
            
            var studentsWithGrades = students.Where(s => s.Grades.Count > 0).ToList();
            if (studentsWithGrades.Count > 0)
            {
                var averageGPA = studentsWithGrades.Average(s => s.CalculateGPA());
                var highestGPA = studentsWithGrades.Max(s => s.CalculateGPA());
                var lowestGPA = studentsWithGrades.Min(s => s.CalculateGPA());

                sb.AppendLine($"متوسط المعدل العام: {averageGPA:F2}");
                sb.AppendLine($"أعلى معدل: {highestGPA:F2}");
                sb.AppendLine($"أقل معدل: {lowestGPA:F2}");

                var topStudent = studentsWithGrades.OrderByDescending(s => s.CalculateGPA()).First();
                sb.AppendLine($"أفضل طالب: {topStudent.Name} (معدل: {topStudent.CalculateGPA():F2})");
            }

            return sb.ToString();
        }

        /// <summary>
        /// إنتاج تقرير درجات مادة معينة
        /// </summary>
        /// <param name="subjectName">اسم المادة</param>
        /// <returns>نص التقرير</returns>
        public string GenerateSubjectReport(string subjectName)
        {
            var sb = new StringBuilder();
            var subject = gradeManager.GetSubjectStatistics(subjectName);

            sb.AppendLine($"=== تقرير مادة: {subjectName} ===");
            sb.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            if (subject == null || subject.Grades.Count == 0)
            {
                sb.AppendLine($"لا توجد درجات في مادة: {subjectName}");
                return sb.ToString();
            }

            sb.AppendLine($"معلومات المادة:");
            sb.AppendLine($"اسم المادة: {subject.Name}");
            sb.AppendLine($"رمز المادة: {subject.Code}");
            sb.AppendLine($"الساعات المعتمدة: {subject.CreditHours}");
            sb.AppendLine($"المدرس: {subject.Instructor}");
            sb.AppendLine();

            sb.AppendLine($"إحصائيات المادة:");
            sb.AppendLine($"عدد الطلاب: {subject.Grades.Count}");
            sb.AppendLine($"متوسط الدرجات: {subject.CalculateAverageScore():F1}%");
            sb.AppendLine($"أعلى درجة: {subject.GetHighestScore():F1}%");
            sb.AppendLine($"أقل درجة: {subject.GetLowestScore():F1}%");
            sb.AppendLine($"عدد الناجحين: {subject.GetPassingStudentsCount()}");
            sb.AppendLine($"عدد الراسبين: {subject.GetFailingStudentsCount()}");
            sb.AppendLine($"نسبة النجاح: {subject.GetPassingRate():F1}%");
            sb.AppendLine();

            sb.AppendLine("درجات الطلاب:");
            sb.AppendLine($"{"معرف الطالب",-12} {"اسم الطالب",-25} {"الدرجة",-10} {"النسبة",-10} {"الحرف",-8} {"النقاط",-8} {"الحالة",-8}");
            sb.AppendLine(new string('-', 90));

            foreach (var grade in subject.Grades.OrderByDescending(g => g.GetPercentage()))
            {
                var student = studentManager.GetStudentById(grade.StudentId);
                var studentName = student?.Name ?? "غير معروف";
                var status = grade.IsPassing() ? "ناجح" : "راسب";
                
                sb.AppendLine($"{grade.StudentId,-12} {studentName,-25} {grade.Score}/{grade.MaxScore,-10} {grade.GetPercentage(),-10:F1}% {grade.LetterGrade,-8} {grade.Points,-8:F1} {status,-8}");
            }

            sb.AppendLine();
            sb.AppendLine("توزيع الدرجات:");
            var distribution = subject.GetGradeDistribution();
            sb.AppendLine($"{"الدرجة الحرفية",-15} {"العدد",-8} {"النسبة",-10}");
            sb.AppendLine(new string('-', 40));

            foreach (var kvp in distribution)
            {
                double percentage = subject.Grades.Count > 0 ? (double)kvp.Value / subject.Grades.Count * 100 : 0;
                sb.AppendLine($"{kvp.Key,-15} {kvp.Value,-8} {percentage,-10:F1}%");
            }

            return sb.ToString();
        }

        /// <summary>
        /// إنتاج تقرير درجات طالب معين
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        /// <returns>نص التقرير</returns>
        public string GenerateStudentReport(int studentId)
        {
            var sb = new StringBuilder();
            var student = studentManager.GetStudentById(studentId);

            if (student == null)
            {
                return "الطالب غير موجود.";
            }

            sb.AppendLine($"=== تقرير الطالب: {student.Name} ===");
            sb.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            sb.AppendLine("معلومات الطالب:");
            sb.AppendLine($"المعرف: {student.Id}");
            sb.AppendLine($"الاسم: {student.Name}");
            sb.AppendLine($"رقم الطالب: {student.StudentNumber}");
            sb.AppendLine($"البريد الإلكتروني: {student.Email}");
            sb.AppendLine($"تاريخ التسجيل: {student.EnrollmentDate:yyyy-MM-dd}");
            sb.AppendLine($"المعدل العام: {student.CalculateGPA():F2}");
            sb.AppendLine();

            var grades = gradeManager.GetStudentGrades(studentId);
            if (grades.Count == 0)
            {
                sb.AppendLine("لا توجد درجات للطالب.");
                return sb.ToString();
            }

            sb.AppendLine("درجات الطالب:");
            sb.AppendLine($"{"المادة",-25} {"الدرجة",-10} {"النسبة",-10} {"الحرف",-8} {"النقاط",-8} {"الحالة",-8} {"التاريخ",-12}");
            sb.AppendLine(new string('-', 90));

            foreach (var grade in grades.OrderBy(g => g.SubjectName))
            {
                var status = grade.IsPassing() ? "ناجح" : "راسب";
                sb.AppendLine($"{grade.SubjectName,-25} {grade.Score}/{grade.MaxScore,-10} {grade.GetPercentage(),-10:F1}% {grade.LetterGrade,-8} {grade.Points,-8:F1} {status,-8} {grade.DateRecorded,-12:yyyy-MM-dd}");
            }

            sb.AppendLine();
            sb.AppendLine("ملخص الأداء:");
            var passingGrades = grades.Where(g => g.IsPassing()).Count();
            var failingGrades = grades.Count - passingGrades;
            sb.AppendLine($"عدد المواد المجتازة: {passingGrades}");
            sb.AppendLine($"عدد المواد الراسبة: {failingGrades}");
            sb.AppendLine($"نسبة النجاح: {(grades.Count > 0 ? (double)passingGrades / grades.Count * 100 : 0):F1}%");

            return sb.ToString();
        }

        /// <summary>
        /// حفظ التقرير في ملف
        /// </summary>
        /// <param name="report">نص التقرير</param>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveReportToFile(string report, string fileName)
        {
            try
            {
                var reportsDirectory = "Reports";
                if (!Directory.Exists(reportsDirectory))
                {
                    Directory.CreateDirectory(reportsDirectory);
                }

                var filePath = Path.Combine(reportsDirectory, fileName);
                File.WriteAllText(filePath, report, Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنتاج تقرير أفضل الطلاب
        /// </summary>
        /// <param name="count">عدد الطلاب المطلوب عرضهم</param>
        /// <returns>نص التقرير</returns>
        public string GenerateTopStudentsReport(int count = 10)
        {
            var sb = new StringBuilder();
            var students = studentManager.GetStudentsSortedByGPA(true);

            sb.AppendLine($"=== تقرير أفضل {Math.Min(count, students.Count)} طلاب ===");
            sb.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            if (students.Count == 0)
            {
                sb.AppendLine("لا يوجد طلاب مسجلين.");
                return sb.ToString();
            }

            sb.AppendLine($"{"الترتيب",-8} {"الاسم",-25} {"رقم الطالب",-15} {"المعدل",-8} {"عدد المواد",-10}");
            sb.AppendLine(new string('-', 70));

            for (int i = 0; i < Math.Min(count, students.Count); i++)
            {
                var student = students[i];
                sb.AppendLine($"{i + 1,-8} {student.Name,-25} {student.StudentNumber,-15} {student.CalculateGPA(),-8:F2} {student.Grades.Count,-10}");
            }

            return sb.ToString();
        }
    }
}
