@echo off
chcp 65001 >nul
title تثبيت نظام رصد درجات الطلاب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🔧 تثبيت نظام رصد درجات الطلاب 🔧                  ║
echo ║                                                              ║
echo ║                    الإصدار 1.0 - 2024                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 جاري فحص النظام...
echo.

REM فحص وجود .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 6.0 من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo أو يمكنك استخدام الملف التنفيذي المستقل الذي لا يحتاج .NET
    echo.
    pause
    goto :end
) else (
    for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
    echo ✅ .NET مثبت - الإصدار: %DOTNET_VERSION%
)

echo.
echo 🔨 جاري بناء النظام...
echo.

REM بناء المشروع
dotnet build -c Release
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    pause
    goto :end
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 📦 جاري إنشاء الملف التنفيذي المستقل...
echo.

REM إنشاء الملف التنفيذي المستقل
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
if errorlevel 1 (
    echo ❌ فشل في إنشاء الملف التنفيذي
    pause
    goto :end
)

echo ✅ تم إنشاء الملف التنفيذي بنجاح
echo.

echo 🗂️ جاري إنشاء المجلدات المطلوبة...

REM إنشاء المجلدات المطلوبة
if not exist "Data" mkdir Data
if not exist "Data\Backup" mkdir "Data\Backup"
if not exist "Reports" mkdir Reports

echo ✅ تم إنشاء المجلدات
echo.

echo 🖥️ هل تريد إنشاء اختصار على سطح المكتب؟ (Y/N)
set /p CREATE_SHORTCUT=

if /i "%CREATE_SHORTCUT%"=="Y" (
    call "إنشاء_اختصار_سطح_المكتب.bat"
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                  ✅ تم التثبيت بنجاح! ✅                     ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎯 طرق تشغيل النظام:
echo.
echo 1️⃣  النقر المزدوج على: تشغيل_النظام.bat
echo 2️⃣  النقر المزدوج على: bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe
echo 3️⃣  استخدام الاختصار على سطح المكتب (إذا تم إنشاؤه)
echo.
echo 📖 لمزيد من المعلومات، راجع: دليل_التشغيل.md
echo.

:end
pause
