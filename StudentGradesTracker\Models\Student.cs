using System;
using System.Collections.Generic;

namespace StudentGradesTracker.Models
{
    /// <summary>
    /// فئة تمثل الطالب
    /// </summary>
    public class Student
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string StudentNumber { get; set; }
        public string Email { get; set; }
        public DateTime EnrollmentDate { get; set; }
        public List<Grade> Grades { get; set; }

        public Student()
        {
            Grades = new List<Grade>();
            EnrollmentDate = DateTime.Now;
        }

        public Student(int id, string name, string studentNumber, string email)
        {
            Id = id;
            Name = name;
            StudentNumber = studentNumber;
            Email = email;
            EnrollmentDate = DateTime.Now;
            Grades = new List<Grade>();
        }

        /// <summary>
        /// حساب المعدل العام للطالب
        /// </summary>
        /// <returns>المعدل العام</returns>
        public double CalculateGPA()
        {
            if (Grades.Count == 0)
                return 0.0;

            double totalPoints = 0;
            int totalCredits = 0;

            foreach (var grade in Grades)
            {
                totalPoints += grade.Points * grade.CreditHours;
                totalCredits += grade.CreditHours;
            }

            return totalCredits > 0 ? totalPoints / totalCredits : 0.0;
        }

        /// <summary>
        /// الحصول على درجة الطالب في مادة معينة
        /// </summary>
        /// <param name="subjectName">اسم المادة</param>
        /// <returns>الدرجة أو null إذا لم توجد</returns>
        public Grade GetGradeBySubject(string subjectName)
        {
            return Grades.Find(g => g.SubjectName.Equals(subjectName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// إضافة أو تحديث درجة الطالب في مادة
        /// </summary>
        /// <param name="grade">الدرجة الجديدة</param>
        public void AddOrUpdateGrade(Grade grade)
        {
            var existingGrade = GetGradeBySubject(grade.SubjectName);
            if (existingGrade != null)
            {
                existingGrade.Score = grade.Score;
                existingGrade.Points = grade.Points;
                existingGrade.LetterGrade = grade.LetterGrade;
                existingGrade.DateRecorded = DateTime.Now;
            }
            else
            {
                Grades.Add(grade);
            }
        }

        public override string ToString()
        {
            return $"ID: {Id}, Name: {Name}, Student#: {StudentNumber}, GPA: {CalculateGPA():F2}";
        }
    }
}
