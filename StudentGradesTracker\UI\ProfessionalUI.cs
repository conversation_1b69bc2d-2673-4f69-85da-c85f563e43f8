using System;
using System.Collections.Generic;
using System.Linq;
using StudentGradesTracker.Models;
using StudentGradesTracker.Services;

namespace StudentGradesTracker.UI
{
    /// <summary>
    /// واجهة المستخدم الاحترافية
    /// </summary>
    public class ProfessionalUI
    {
        private StudentManager studentManager;
        private GradeManager gradeManager;
        private DataManager dataManager;
        private ReportGenerator reportGenerator;

        public ProfessionalUI(StudentManager studentManager, GradeManager gradeManager)
        {
            this.studentManager = studentManager;
            this.gradeManager = gradeManager;
            dataManager = new DataManager();
            reportGenerator = new ReportGenerator(studentManager, gradeManager);
        }

        /// <summary>
        /// تشغيل التطبيق الرئيسي
        /// </summary>
        public void Run()
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            ShowWelcomeScreen();

            while (true)
            {
                ShowMainMenu();
                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        ManageStudents();
                        break;
                    case "2":
                        ManageGrades();
                        break;
                    case "3":
                        ShowReports();
                        break;
                    case "4":
                        ShowStatistics();
                        break;
                    case "5":
                        ManageData();
                        break;
                    case "6":
                        GenerateReports();
                        break;
                    case "0":
                        ShowExitScreen();
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح، يرجى المحاولة مرة أخرى");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// عرض شاشة الترحيب
        /// </summary>
        private void ShowWelcomeScreen()
        {
            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("║        🎓 نظام رصد درجات الطلاب - مادة تقنية المعلومات        ║");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("║                    الإصدار 1.0 - 2024                      ║");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            
            Console.ResetColor();
            Console.WriteLine();
            
            UIHelper.PrintInfo($"📅 التاريخ: {UIHelper.FormatDate(DateTime.Now)}");
            UIHelper.PrintInfo($"🕐 الوقت: {DateTime.Now:HH:mm}");
            Console.WriteLine();
            
            UIHelper.PrintInfo("جاري تحميل النظام...");
            
            // محاكاة شريط التحميل
            for (int i = 0; i <= 100; i += 10)
            {
                Console.Write("\r");
                UIHelper.PrintProgressBar(i, 100);
                System.Threading.Thread.Sleep(100);
            }
            
            UIHelper.PrintSuccess("تم تحميل النظام بنجاح!");
            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض القائمة الرئيسية
        /// </summary>
        private void ShowMainMenu()
        {
            UIHelper.PrintHeader("القائمة الرئيسية");
            
            // عرض إحصائيات سريعة
            var students = studentManager.GetAllStudents();
            var subjects = gradeManager.GetAllSubjects();
            
            Console.WriteLine("📊 نظرة سريعة:");
            UIHelper.PrintStat("👥", "إجمالي الطلاب", students.Count.ToString(), ConsoleColor.Green);
            UIHelper.PrintStat("📚", "إجمالي المواد", subjects.Count.ToString(), ConsoleColor.Blue);
            UIHelper.PrintStat("📅", "التاريخ", UIHelper.FormatDate(DateTime.Now), ConsoleColor.Yellow);
            
            UIHelper.PrintSeparator();
            Console.WriteLine();

            UIHelper.PrintMenuItem("1", "👥 إدارة الطلاب");
            UIHelper.PrintMenuItem("2", "📝 إدارة الدرجات");
            UIHelper.PrintMenuItem("3", "📊 التقارير السريعة");
            UIHelper.PrintMenuItem("4", "📈 الإحصائيات المفصلة");
            UIHelper.PrintMenuItem("5", "💾 إدارة البيانات");
            UIHelper.PrintMenuItem("6", "📄 إنتاج التقارير");
            Console.WriteLine();
            UIHelper.PrintMenuItem("0", "🚪 خروج");
            
            UIHelper.PrintSeparator();
        }

        /// <summary>
        /// إدارة الطلاب
        /// </summary>
        private void ManageStudents()
        {
            while (true)
            {
                UIHelper.PrintHeader("إدارة الطلاب");
                
                var students = studentManager.GetAllStudents();
                UIHelper.PrintStat("👥", "عدد الطلاب المسجلين", students.Count.ToString(), ConsoleColor.Green);
                Console.WriteLine();

                UIHelper.PrintMenuItem("1", "➕ إضافة طالب جديد");
                UIHelper.PrintMenuItem("2", "📋 عرض جميع الطلاب");
                UIHelper.PrintMenuItem("3", "🔍 البحث عن طالب");
                UIHelper.PrintMenuItem("4", "✏️ تعديل بيانات طالب");
                UIHelper.PrintMenuItem("5", "🗑️ حذف طالب");
                Console.WriteLine();
                UIHelper.PrintMenuItem("0", "↩️ العودة للقائمة الرئيسية");
                
                UIHelper.PrintSeparator();

                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        AddStudent();
                        break;
                    case "2":
                        ShowAllStudents();
                        break;
                    case "3":
                        SearchStudent();
                        break;
                    case "4":
                        UpdateStudent();
                        break;
                    case "5":
                        DeleteStudent();
                        break;
                    case "0":
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// إضافة طالب جديد
        /// </summary>
        private void AddStudent()
        {
            UIHelper.PrintSubHeader("إضافة طالب جديد");

            var name = UIHelper.GetInput("اسم الطالب");
            if (string.IsNullOrWhiteSpace(name))
            {
                UIHelper.PrintError("اسم الطالب مطلوب");
                UIHelper.WaitForKey();
                return;
            }

            var studentNumber = UIHelper.GetInput("رقم الطالب");
            if (string.IsNullOrWhiteSpace(studentNumber))
            {
                UIHelper.PrintError("رقم الطالب مطلوب");
                UIHelper.WaitForKey();
                return;
            }

            var email = UIHelper.GetInput("البريد الإلكتروني");
            if (string.IsNullOrWhiteSpace(email))
            {
                UIHelper.PrintError("البريد الإلكتروني مطلوب");
                UIHelper.WaitForKey();
                return;
            }

            try
            {
                var student = studentManager.AddStudent(name, studentNumber, email);
                UIHelper.PrintSuccess($"تم إضافة الطالب بنجاح!");
                Console.WriteLine();
                
                // عرض بيانات الطالب المضاف
                UIHelper.PrintInfo("بيانات الطالب المضاف:");
                UIHelper.PrintStat("🆔", "المعرف", student.Id.ToString(), ConsoleColor.Cyan);
                UIHelper.PrintStat("👤", "الاسم", student.Name, ConsoleColor.Green);
                UIHelper.PrintStat("🎫", "رقم الطالب", student.StudentNumber, ConsoleColor.Blue);
                UIHelper.PrintStat("📧", "البريد الإلكتروني", student.Email, ConsoleColor.Yellow);
                UIHelper.PrintStat("📅", "تاريخ التسجيل", UIHelper.FormatDate(student.EnrollmentDate), ConsoleColor.Magenta);
            }
            catch (ArgumentException ex)
            {
                UIHelper.PrintError($"خطأ: {ex.Message}");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض جميع الطلاب
        /// </summary>
        private void ShowAllStudents()
        {
            UIHelper.PrintSubHeader("قائمة جميع الطلاب");

            var students = studentManager.GetAllStudents();
            if (students.Count == 0)
            {
                UIHelper.PrintWarning("لا يوجد طلاب مسجلين في النظام");
                UIHelper.WaitForKey();
                return;
            }

            // إعداد الجدول
            string[] headers = { "المعرف", "الاسم", "رقم الطالب", "البريد الإلكتروني", "المعدل", "تاريخ التسجيل" };
            int[] widths = { 8, 25, 15, 30, 8, 12 };

            UIHelper.PrintTableHeader(headers, widths);

            foreach (var student in students.OrderBy(s => s.Name))
            {
                string[] values = {
                    student.Id.ToString(),
                    student.Name,
                    student.StudentNumber,
                    student.Email,
                    student.CalculateGPA().ToString("F2"),
                    UIHelper.FormatDate(student.EnrollmentDate)
                };
                UIHelper.PrintTableRow(values, widths);
            }

            UIHelper.PrintTableFooter(widths);
            Console.WriteLine();
            UIHelper.PrintStat("📊", "إجمالي عدد الطلاب", students.Count.ToString(), ConsoleColor.Green);

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// شاشة الخروج
        /// </summary>
        private void ShowExitScreen()
        {
            UIHelper.PrintHeader("إنهاء البرنامج");
            
            UIHelper.PrintInfo("جاري حفظ البيانات...");
            if (dataManager.SaveAllData(studentManager, gradeManager))
            {
                UIHelper.PrintSuccess("تم حفظ البيانات بنجاح!");
            }
            else
            {
                UIHelper.PrintWarning("تحذير: حدث خطأ في حفظ البيانات");
            }

            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("║                   شكراً لاستخدام النظام!                    ║");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("║              🎓 نظام رصد درجات الطلاب 2024                  ║");
            Console.WriteLine("║                                                              ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();
            
            Console.WriteLine();
            UIHelper.PrintInfo($"📅 تم إنهاء الجلسة في: {UIHelper.FormatDateTime(DateTime.Now)}");
        }

        /// <summary>
        /// البحث عن طالب
        /// </summary>
        private void SearchStudent()
        {
            UIHelper.PrintSubHeader("البحث عن طالب");

            UIHelper.PrintMenuItem("1", "🆔 البحث بالمعرف");
            UIHelper.PrintMenuItem("2", "🎫 البحث برقم الطالب");
            UIHelper.PrintMenuItem("3", "👤 البحث بالاسم");
            Console.WriteLine();

            var choice = UIHelper.GetInput("اختر طريقة البحث");
            Student student = null;
            List<Student> students = null;

            switch (choice)
            {
                case "1":
                    var idInput = UIHelper.GetInput("أدخل المعرف");
                    if (int.TryParse(idInput, out int id))
                    {
                        student = studentManager.GetStudentById(id);
                    }
                    else
                    {
                        UIHelper.PrintError("معرف غير صحيح");
                        UIHelper.WaitForKey();
                        return;
                    }
                    break;
                case "2":
                    var studentNumber = UIHelper.GetInput("أدخل رقم الطالب");
                    student = studentManager.GetStudentByNumber(studentNumber);
                    break;
                case "3":
                    var name = UIHelper.GetInput("أدخل الاسم أو جزء منه");
                    students = studentManager.SearchStudentsByName(name);
                    break;
                default:
                    UIHelper.PrintError("خيار غير صحيح");
                    UIHelper.WaitForKey();
                    return;
            }

            Console.WriteLine();

            if (student != null)
            {
                UIHelper.PrintSuccess("تم العثور على الطالب:");
                ShowStudentDetails(student);
            }
            else if (students != null && students.Count > 0)
            {
                UIHelper.PrintSuccess($"تم العثور على {students.Count} طالب:");
                Console.WriteLine();

                string[] headers = { "المعرف", "الاسم", "رقم الطالب", "المعدل" };
                int[] widths = { 8, 25, 15, 8 };

                UIHelper.PrintTableHeader(headers, widths);

                foreach (var s in students)
                {
                    string[] values = {
                        s.Id.ToString(),
                        s.Name,
                        s.StudentNumber,
                        s.CalculateGPA().ToString("F2")
                    };
                    UIHelper.PrintTableRow(values, widths);
                }

                UIHelper.PrintTableFooter(widths);
            }
            else
            {
                UIHelper.PrintWarning("لم يتم العثور على أي طالب");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض تفاصيل طالب
        /// </summary>
        /// <param name="student">الطالب</param>
        private void ShowStudentDetails(Student student)
        {
            Console.WriteLine();
            UIHelper.PrintStat("🆔", "المعرف", student.Id.ToString(), ConsoleColor.Cyan);
            UIHelper.PrintStat("👤", "الاسم", student.Name, ConsoleColor.Green);
            UIHelper.PrintStat("🎫", "رقم الطالب", student.StudentNumber, ConsoleColor.Blue);
            UIHelper.PrintStat("📧", "البريد الإلكتروني", student.Email, ConsoleColor.Yellow);
            UIHelper.PrintStat("📅", "تاريخ التسجيل", UIHelper.FormatDate(student.EnrollmentDate), ConsoleColor.Magenta);
            UIHelper.PrintStat("📊", "المعدل العام", student.CalculateGPA().ToString("F2"), ConsoleColor.Red);

            var grades = gradeManager.GetStudentGrades(student.Id);
            if (grades.Count > 0)
            {
                Console.WriteLine();
                UIHelper.PrintInfo("درجات الطالب:");

                string[] headers = { "المادة", "الدرجة", "النسبة", "الحرف", "النقاط", "التاريخ" };
                int[] widths = { 20, 10, 8, 6, 6, 12 };

                UIHelper.PrintTableHeader(headers, widths);

                foreach (var grade in grades)
                {
                    string[] values = {
                        grade.SubjectName,
                        $"{grade.Score}/{grade.MaxScore}",
                        $"{grade.GetPercentage():F1}%",
                        grade.LetterGrade,
                        grade.Points.ToString("F1"),
                        UIHelper.FormatDate(grade.DateRecorded)
                    };
                    UIHelper.PrintTableRow(values, widths);
                }

                UIHelper.PrintTableFooter(widths);
            }
            else
            {
                UIHelper.PrintWarning("لا توجد درجات للطالب");
            }
        }

        /// <summary>
        /// تعديل بيانات طالب
        /// </summary>
        private void UpdateStudent()
        {
            UIHelper.PrintSubHeader("تعديل بيانات طالب");

            var idInput = UIHelper.GetInput("أدخل معرف الطالب");
            if (!int.TryParse(idInput, out int id))
            {
                UIHelper.PrintError("معرف غير صحيح");
                UIHelper.WaitForKey();
                return;
            }

            var student = studentManager.GetStudentById(id);
            if (student == null)
            {
                UIHelper.PrintError("الطالب غير موجود");
                UIHelper.WaitForKey();
                return;
            }

            UIHelper.PrintInfo("البيانات الحالية:");
            ShowStudentDetails(student);

            Console.WriteLine();
            UIHelper.PrintInfo("أدخل البيانات الجديدة (اتركها فارغة للاحتفاظ بالقيمة الحالية):");

            var newName = UIHelper.GetInput($"الاسم الجديد (الحالي: {student.Name})");
            if (string.IsNullOrWhiteSpace(newName))
                newName = student.Name;

            var newEmail = UIHelper.GetInput($"البريد الإلكتروني الجديد (الحالي: {student.Email})");
            if (string.IsNullOrWhiteSpace(newEmail))
                newEmail = student.Email;

            if (studentManager.UpdateStudent(id, newName, newEmail))
            {
                UIHelper.PrintSuccess("تم تحديث بيانات الطالب بنجاح!");
                Console.WriteLine();
                UIHelper.PrintInfo("البيانات المحدثة:");
                ShowStudentDetails(studentManager.GetStudentById(id));
            }
            else
            {
                UIHelper.PrintError("فشل في تحديث بيانات الطالب");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// حذف طالب
        /// </summary>
        private void DeleteStudent()
        {
            UIHelper.PrintSubHeader("حذف طالب");

            var idInput = UIHelper.GetInput("أدخل معرف الطالب");
            if (!int.TryParse(idInput, out int id))
            {
                UIHelper.PrintError("معرف غير صحيح");
                UIHelper.WaitForKey();
                return;
            }

            var student = studentManager.GetStudentById(id);
            if (student == null)
            {
                UIHelper.PrintError("الطالب غير موجود");
                UIHelper.WaitForKey();
                return;
            }

            UIHelper.PrintWarning("سيتم حذف الطالب التالي:");
            ShowStudentDetails(student);

            Console.WriteLine();
            var confirm = UIHelper.GetInput("هل أنت متأكد من الحذف؟ (نعم/لا)");

            if (confirm.ToLower() == "نعم" || confirm.ToLower() == "y" || confirm.ToLower() == "yes")
            {
                if (studentManager.DeleteStudent(id))
                {
                    UIHelper.PrintSuccess("تم حذف الطالب بنجاح!");
                }
                else
                {
                    UIHelper.PrintError("فشل في حذف الطالب");
                }
            }
            else
            {
                UIHelper.PrintInfo("تم إلغاء عملية الحذف");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// إدارة الدرجات
        /// </summary>
        private void ManageGrades()
        {
            while (true)
            {
                UIHelper.PrintHeader("إدارة الدرجات");

                var subjects = gradeManager.GetAllSubjects();
                var totalGrades = subjects.Sum(s => s.Grades.Count);

                UIHelper.PrintStat("📚", "عدد المواد", subjects.Count.ToString(), ConsoleColor.Blue);
                UIHelper.PrintStat("📝", "إجمالي الدرجات", totalGrades.ToString(), ConsoleColor.Green);
                Console.WriteLine();

                UIHelper.PrintMenuItem("1", "➕ إضافة درجة");
                UIHelper.PrintMenuItem("2", "✏️ تعديل درجة");
                UIHelper.PrintMenuItem("3", "🗑️ حذف درجة");
                UIHelper.PrintMenuItem("4", "👤 عرض درجات طالب");
                UIHelper.PrintMenuItem("5", "📚 عرض درجات مادة");
                UIHelper.PrintMenuItem("6", "📊 إحصائيات الدرجات");
                Console.WriteLine();
                UIHelper.PrintMenuItem("0", "↩️ العودة للقائمة الرئيسية");

                UIHelper.PrintSeparator();

                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        AddGrade();
                        break;
                    case "2":
                        UpdateGrade();
                        break;
                    case "3":
                        DeleteGrade();
                        break;
                    case "4":
                        ShowStudentGrades();
                        break;
                    case "5":
                        ShowSubjectGrades();
                        break;
                    case "6":
                        ShowGradeStatistics();
                        break;
                    case "0":
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// إضافة درجة
        /// </summary>
        private void AddGrade()
        {
            UIHelper.PrintSubHeader("إضافة درجة جديدة");

            var studentIdInput = UIHelper.GetInput("معرف الطالب");
            if (!int.TryParse(studentIdInput, out int studentId))
            {
                UIHelper.PrintError("معرف الطالب غير صحيح");
                UIHelper.WaitForKey();
                return;
            }

            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                UIHelper.PrintError("الطالب غير موجود");
                UIHelper.WaitForKey();
                return;
            }

            UIHelper.PrintSuccess($"الطالب: {student.Name} ({student.StudentNumber})");
            Console.WriteLine();

            var subjectName = UIHelper.GetInput("اسم المادة (افتراضي: تقنية المعلومات)");
            if (string.IsNullOrWhiteSpace(subjectName))
                subjectName = "تقنية المعلومات";

            var scoreInput = UIHelper.GetInput("الدرجة");
            if (!double.TryParse(scoreInput, out double score))
            {
                UIHelper.PrintError("درجة غير صحيحة");
                UIHelper.WaitForKey();
                return;
            }

            var maxScoreInput = UIHelper.GetInput("الدرجة القصوى (افتراضي: 100)");
            double maxScore = 100.0;
            if (!string.IsNullOrWhiteSpace(maxScoreInput))
            {
                if (!double.TryParse(maxScoreInput, out maxScore))
                {
                    UIHelper.PrintWarning("درجة قصوى غير صحيحة، سيتم استخدام 100");
                    maxScore = 100.0;
                }
            }

            var semester = UIHelper.GetInput("الفصل الدراسي (اختياري)");

            if (gradeManager.AddGrade(studentId, subjectName, score, maxScore, semester))
            {
                UIHelper.PrintSuccess("تم إضافة الدرجة بنجاح!");

                // عرض تفاصيل الدرجة المضافة
                var grade = student.GetGradeBySubject(subjectName);
                if (grade != null)
                {
                    Console.WriteLine();
                    UIHelper.PrintInfo("تفاصيل الدرجة المضافة:");
                    UIHelper.PrintStat("📚", "المادة", grade.SubjectName, ConsoleColor.Blue);
                    UIHelper.PrintStat("📝", "الدرجة", $"{grade.Score}/{grade.MaxScore}", ConsoleColor.Green);
                    UIHelper.PrintStat("📊", "النسبة المئوية", $"{grade.GetPercentage():F1}%", ConsoleColor.Yellow);
                    UIHelper.PrintStat("🏆", "الدرجة الحرفية", grade.LetterGrade, ConsoleColor.Magenta);
                    UIHelper.PrintStat("⭐", "النقاط", grade.Points.ToString("F1"), ConsoleColor.Cyan);
                    UIHelper.PrintStat("📅", "تاريخ التسجيل", UIHelper.FormatDate(grade.DateRecorded), ConsoleColor.Gray);
                }
            }
            else
            {
                UIHelper.PrintError("فشل في إضافة الدرجة");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض درجات طالب
        /// </summary>
        private void ShowStudentGrades()
        {
            UIHelper.PrintSubHeader("عرض درجات طالب");

            var studentIdInput = UIHelper.GetInput("معرف الطالب");
            if (!int.TryParse(studentIdInput, out int studentId))
            {
                UIHelper.PrintError("معرف الطالب غير صحيح");
                UIHelper.WaitForKey();
                return;
            }

            var student = studentManager.GetStudentById(studentId);
            if (student == null)
            {
                UIHelper.PrintError("الطالب غير موجود");
                UIHelper.WaitForKey();
                return;
            }

            ShowStudentDetails(student);
            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض التقارير السريعة
        /// </summary>
        private void ShowReports()
        {
            while (true)
            {
                UIHelper.PrintHeader("التقارير السريعة");

                UIHelper.PrintMenuItem("1", "🏆 تقرير أفضل الطلاب");
                UIHelper.PrintMenuItem("2", "📊 تقرير الطلاب حسب المعدل");
                UIHelper.PrintMenuItem("3", "✅ تقرير الطلاب الناجحين والراسبين");
                UIHelper.PrintMenuItem("4", "📈 تقرير توزيع الدرجات");
                Console.WriteLine();
                UIHelper.PrintMenuItem("0", "↩️ العودة للقائمة الرئيسية");

                UIHelper.PrintSeparator();

                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        ShowTopStudentsReport();
                        break;
                    case "2":
                        ShowStudentsByGPAReport();
                        break;
                    case "3":
                        ShowPassFailReport();
                        break;
                    case "4":
                        ShowGradeDistributionReport();
                        break;
                    case "0":
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// تقرير أفضل الطلاب
        /// </summary>
        private void ShowTopStudentsReport()
        {
            UIHelper.PrintSubHeader("تقرير أفضل الطلاب");

            var students = studentManager.GetStudentsSortedByGPA(true);
            if (students.Count == 0)
            {
                UIHelper.PrintWarning("لا يوجد طلاب مسجلين");
                UIHelper.WaitForKey();
                return;
            }

            var topCount = Math.Min(10, students.Count);
            UIHelper.PrintInfo($"أفضل {topCount} طلاب:");
            Console.WriteLine();

            string[] headers = { "الترتيب", "الاسم", "رقم الطالب", "المعدل", "عدد المواد" };
            int[] widths = { 8, 25, 15, 8, 10 };

            UIHelper.PrintTableHeader(headers, widths);

            for (int i = 0; i < topCount; i++)
            {
                var student = students[i];
                string[] values = {
                    $"#{i + 1}",
                    student.Name,
                    student.StudentNumber,
                    student.CalculateGPA().ToString("F2"),
                    student.Grades.Count.ToString()
                };
                UIHelper.PrintTableRow(values, widths);
            }

            UIHelper.PrintTableFooter(widths);

            if (students.Count > 0)
            {
                Console.WriteLine();
                var topStudent = students[0];
                UIHelper.PrintStat("🥇", "الطالب الأول", $"{topStudent.Name} - معدل: {topStudent.CalculateGPA():F2}", ConsoleColor.Yellow);
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض الإحصائيات المفصلة
        /// </summary>
        private void ShowStatistics()
        {
            UIHelper.PrintHeader("الإحصائيات المفصلة");

            var students = studentManager.GetAllStudents();
            var subjects = gradeManager.GetAllSubjects();

            // إحصائيات عامة
            UIHelper.PrintSubHeader("الإحصائيات العامة");
            UIHelper.PrintStat("👥", "إجمالي عدد الطلاب", students.Count.ToString(), ConsoleColor.Green);
            UIHelper.PrintStat("📚", "إجمالي عدد المواد", subjects.Count.ToString(), ConsoleColor.Blue);

            if (students.Count > 0)
            {
                var studentsWithGrades = students.Where(s => s.Grades.Count > 0).ToList();
                if (studentsWithGrades.Count > 0)
                {
                    var averageGPA = studentsWithGrades.Average(s => s.CalculateGPA());
                    var highestGPA = studentsWithGrades.Max(s => s.CalculateGPA());
                    var lowestGPA = studentsWithGrades.Min(s => s.CalculateGPA());

                    UIHelper.PrintStat("📊", "متوسط المعدل العام", averageGPA.ToString("F2"), ConsoleColor.Yellow);
                    UIHelper.PrintStat("🔝", "أعلى معدل", highestGPA.ToString("F2"), ConsoleColor.Green);
                    UIHelper.PrintStat("🔻", "أقل معدل", lowestGPA.ToString("F2"), ConsoleColor.Red);

                    var topStudent = studentsWithGrades.OrderByDescending(s => s.CalculateGPA()).First();
                    UIHelper.PrintStat("🏆", "أفضل طالب", $"{topStudent.Name} ({topStudent.CalculateGPA():F2})", ConsoleColor.Yellow);
                }
            }

            Console.WriteLine();

            // إحصائيات المواد
            foreach (var subject in subjects)
            {
                if (subject.Grades.Count > 0)
                {
                    UIHelper.PrintSubHeader($"إحصائيات مادة {subject.Name}");
                    UIHelper.PrintStat("👥", "عدد الطلاب", subject.Grades.Count.ToString(), ConsoleColor.Cyan);
                    UIHelper.PrintStat("📊", "متوسط الدرجات", $"{subject.CalculateAverageScore():F1}%", ConsoleColor.Yellow);
                    UIHelper.PrintStat("✅", "نسبة النجاح", $"{subject.GetPassingRate():F1}%", ConsoleColor.Green);
                    UIHelper.PrintStat("🔝", "أعلى درجة", $"{subject.GetHighestScore():F1}%", ConsoleColor.Blue);
                    UIHelper.PrintStat("🔻", "أقل درجة", $"{subject.GetLowestScore():F1}%", ConsoleColor.Red);
                    Console.WriteLine();
                }
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// إدارة البيانات
        /// </summary>
        private void ManageData()
        {
            while (true)
            {
                UIHelper.PrintHeader("إدارة البيانات");

                UIHelper.PrintMenuItem("1", "💾 حفظ البيانات");
                UIHelper.PrintMenuItem("2", "📂 تحميل البيانات");
                UIHelper.PrintMenuItem("3", "🔄 إنشاء نسخة احتياطية");
                UIHelper.PrintMenuItem("4", "📊 تصدير البيانات إلى CSV");
                UIHelper.PrintMenuItem("5", "📋 معلومات البيانات");
                Console.WriteLine();
                UIHelper.PrintMenuItem("0", "↩️ العودة للقائمة الرئيسية");

                UIHelper.PrintSeparator();

                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        SaveData();
                        break;
                    case "2":
                        LoadData();
                        break;
                    case "3":
                        CreateBackup();
                        break;
                    case "4":
                        ExportToCSV();
                        break;
                    case "5":
                        ShowDataInfo();
                        break;
                    case "0":
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        private void SaveData()
        {
            UIHelper.PrintSubHeader("حفظ البيانات");

            UIHelper.PrintInfo("جاري حفظ البيانات...");

            if (dataManager.SaveAllData(studentManager, gradeManager))
            {
                UIHelper.PrintSuccess("تم حفظ البيانات بنجاح!");
                UIHelper.PrintInfo($"📅 تاريخ الحفظ: {UIHelper.FormatDateTime(DateTime.Now)}");
            }
            else
            {
                UIHelper.PrintError("فشل في حفظ البيانات");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private void CreateBackup()
        {
            UIHelper.PrintSubHeader("إنشاء نسخة احتياطية");

            UIHelper.PrintInfo("جاري إنشاء النسخة الاحتياطية...");

            if (dataManager.CreateBackup())
            {
                UIHelper.PrintSuccess("تم إنشاء النسخة الاحتياطية بنجاح!");
                UIHelper.PrintInfo($"📅 تاريخ النسخة الاحتياطية: {UIHelper.FormatDateTime(DateTime.Now)}");
                UIHelper.PrintInfo("📁 الموقع: Data/Backup/");
            }
            else
            {
                UIHelper.PrintError("فشل في إنشاء النسخة الاحتياطية");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// تصدير البيانات إلى CSV
        /// </summary>
        private void ExportToCSV()
        {
            UIHelper.PrintSubHeader("تصدير البيانات إلى CSV");

            var fileName = UIHelper.GetInput("اسم الملف (بدون امتداد)");
            if (string.IsNullOrWhiteSpace(fileName))
            {
                fileName = $"students_export_{DateTime.Now:yyyyMMdd_HHmmss}";
            }

            fileName += ".csv";

            var students = studentManager.GetAllStudents();
            UIHelper.PrintInfo("جاري تصدير البيانات...");

            if (dataManager.ExportToCSV(students, fileName))
            {
                UIHelper.PrintSuccess($"تم تصدير البيانات بنجاح!");
                UIHelper.PrintInfo($"📄 اسم الملف: {fileName}");
                UIHelper.PrintInfo($"📁 الموقع: Data/{fileName}");
                UIHelper.PrintInfo($"📊 عدد الطلاب المصدرة: {students.Count}");
            }
            else
            {
                UIHelper.PrintError("فشل في تصدير البيانات");
            }

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// عرض معلومات البيانات
        /// </summary>
        private void ShowDataInfo()
        {
            UIHelper.PrintSubHeader("معلومات البيانات");

            var students = studentManager.GetAllStudents();
            var subjects = gradeManager.GetAllSubjects();
            var totalGrades = subjects.Sum(s => s.Grades.Count);

            UIHelper.PrintStat("👥", "عدد الطلاب", students.Count.ToString(), ConsoleColor.Green);
            UIHelper.PrintStat("📚", "عدد المواد", subjects.Count.ToString(), ConsoleColor.Blue);
            UIHelper.PrintStat("📝", "إجمالي الدرجات", totalGrades.ToString(), ConsoleColor.Yellow);
            UIHelper.PrintStat("💾", "حالة ملفات البيانات", dataManager.DataFilesExist() ? "موجودة" : "غير موجودة", ConsoleColor.Cyan);
            UIHelper.PrintStat("📅", "التاريخ الحالي", UIHelper.FormatDateTime(DateTime.Now), ConsoleColor.Magenta);

            UIHelper.WaitForKey();
        }

        /// <summary>
        /// إنتاج التقارير المفصلة
        /// </summary>
        private void GenerateReports()
        {
            while (true)
            {
                UIHelper.PrintHeader("إنتاج التقارير المفصلة");

                UIHelper.PrintMenuItem("1", "📋 تقرير شامل عن الطلاب");
                UIHelper.PrintMenuItem("2", "📚 تقرير مادة معينة");
                UIHelper.PrintMenuItem("3", "👤 تقرير طالب معين");
                UIHelper.PrintMenuItem("4", "🏆 تقرير أفضل الطلاب");
                Console.WriteLine();
                UIHelper.PrintMenuItem("0", "↩️ العودة للقائمة الرئيسية");

                UIHelper.PrintSeparator();

                var choice = UIHelper.GetInput("اختر من القائمة");

                switch (choice)
                {
                    case "1":
                        GenerateStudentsReport();
                        break;
                    case "2":
                        GenerateSubjectReport();
                        break;
                    case "3":
                        GenerateStudentReport();
                        break;
                    case "4":
                        GenerateTopStudentsReport();
                        break;
                    case "0":
                        return;
                    default:
                        UIHelper.PrintError("خيار غير صحيح");
                        UIHelper.WaitForKey();
                        break;
                }
            }
        }

        /// <summary>
        /// إنتاج تقرير الطلاب
        /// </summary>
        private void GenerateStudentsReport()
        {
            UIHelper.PrintSubHeader("تقرير شامل عن الطلاب");

            UIHelper.PrintInfo("جاري إنتاج التقرير...");
            var report = reportGenerator.GenerateStudentsReport();

            Console.WriteLine();
            Console.WriteLine(report);

            var save = UIHelper.GetInput("هل تريد حفظ التقرير في ملف؟ (نعم/لا)");

            if (save.ToLower() == "نعم" || save.ToLower() == "y" || save.ToLower() == "yes")
            {
                var fileName = $"students_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                if (reportGenerator.SaveReportToFile(report, fileName))
                {
                    UIHelper.PrintSuccess($"تم حفظ التقرير في ملف: Reports/{fileName}");
                }
                else
                {
                    UIHelper.PrintError("فشل في حفظ التقرير");
                }
            }

            UIHelper.WaitForKey();
        }

        // باقي الوظائف المساعدة...
        private void UpdateGrade() { /* تنفيذ تعديل الدرجة */ }
        private void DeleteGrade() { /* تنفيذ حذف الدرجة */ }
        private void ShowSubjectGrades() { /* تنفيذ عرض درجات المادة */ }
        private void ShowGradeStatistics() { /* تنفيذ إحصائيات الدرجات */ }
        private void ShowStudentsByGPAReport() { /* تنفيذ تقرير الطلاب حسب المعدل */ }
        private void ShowPassFailReport() { /* تنفيذ تقرير النجاح والرسوب */ }
        private void ShowGradeDistributionReport() { /* تنفيذ تقرير توزيع الدرجات */ }
        private void LoadData() { /* تنفيذ تحميل البيانات */ }
        private void GenerateSubjectReport() { /* تنفيذ تقرير المادة */ }
        private void GenerateStudentReport() { /* تنفيذ تقرير الطالب */ }
        private void GenerateTopStudentsReport() { /* تنفيذ تقرير أفضل الطلاب */ }
    }
}
