@echo off
title Student Grades Tracker System

echo.
echo ===============================================
echo    Student Grades Tracker System
echo    Version 1.0 - 2024
echo ===============================================
echo.

echo Checking system...
echo.

REM Check if dotnet is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed
    echo.
    echo Please download and install .NET 6.0 from:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    pause
    exit /b 1
)

echo .NET is available
echo.

REM Check if project files exist
if not exist "StudentGradesTracker.csproj" (
    echo ERROR: Project files not found
    echo Make sure you are in the correct directory
    pause
    exit /b 1
)

echo Project files found
echo.

echo Starting the system...
echo.

REM Run the application
dotnet run

if errorlevel 1 (
    echo.
    echo ERROR: Failed to run the application
    echo.
    echo Trying to build the project first...
    dotnet build
    if errorlevel 1 (
        echo ERROR: Failed to build the project
        pause
        exit /b 1
    )
    
    echo Build successful
    echo Starting the system again...
    dotnet run
)

echo.
echo System ended
pause
