@echo off
chcp 65001 >nul
title نظام رصد درجات الطلاب - التشغيل المباشر

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🎓 نظام رصد درجات الطلاب - التشغيل المباشر           ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري فحص النظام...
echo.

REM التحقق من وجود dotnet
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 6.0 من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo أو استخدم الرابط المباشر:
    echo https://aka.ms/dotnet-core-applaunch?missing_runtime=true^&arch=x64^&rid=win10-x64^&apphost_version=6.0.0
    echo.
    pause
    exit /b 1
)

echo ✅ .NET متوفر
echo.

REM التحقق من وجود ملفات المشروع
if not exist "StudentGradesTracker.csproj" (
    echo ❌ ملفات المشروع غير موجودة
    echo تأكد من أنك في المجلد الصحيح
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM تشغيل التطبيق مباشرة
dotnet run

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo.
    echo 🔧 جاري المحاولة ببناء المشروع أولاً...
    dotnet build
    if errorlevel 1 (
        echo ❌ فشل في بناء المشروع
        pause
        exit /b 1
    )
    
    echo ✅ تم بناء المشروع بنجاح
    echo 🚀 جاري تشغيل النظام مرة أخرى...
    dotnet run
)

echo.
echo 📋 تم إنهاء النظام
pause
