using System;

namespace StudentGradesTracker.Models
{
    /// <summary>
    /// فئة تمثل درجة الطالب في مادة معينة
    /// </summary>
    public class Grade
    {
        public int Id { get; set; }
        public int StudentId { get; set; }
        public string SubjectName { get; set; }
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public string LetterGrade { get; set; }
        public double Points { get; set; }
        public int CreditHours { get; set; }
        public DateTime DateRecorded { get; set; }
        public string Semester { get; set; }
        public int Year { get; set; }

        public Grade()
        {
            DateRecorded = DateTime.Now;
            MaxScore = 100.0;
            CreditHours = 3;
            Year = DateTime.Now.Year;
        }

        public Grade(int studentId, string subjectName, double score, double maxScore = 100.0, int creditHours = 3)
        {
            StudentId = studentId;
            SubjectName = subjectName;
            Score = score;
            MaxScore = maxScore;
            CreditHours = creditHours;
            DateRecorded = DateTime.Now;
            Year = DateTime.Now.Year;
            
            CalculateLetterGradeAndPoints();
        }

        /// <summary>
        /// حساب الدرجة الحرفية والنقاط بناءً على النتيجة
        /// </summary>
        public void CalculateLetterGradeAndPoints()
        {
            double percentage = (Score / MaxScore) * 100;

            if (percentage >= 90)
            {
                LetterGrade = "A+";
                Points = 4.0;
            }
            else if (percentage >= 85)
            {
                LetterGrade = "A";
                Points = 3.7;
            }
            else if (percentage >= 80)
            {
                LetterGrade = "B+";
                Points = 3.3;
            }
            else if (percentage >= 75)
            {
                LetterGrade = "B";
                Points = 3.0;
            }
            else if (percentage >= 70)
            {
                LetterGrade = "C+";
                Points = 2.7;
            }
            else if (percentage >= 65)
            {
                LetterGrade = "C";
                Points = 2.3;
            }
            else if (percentage >= 60)
            {
                LetterGrade = "D+";
                Points = 2.0;
            }
            else if (percentage >= 50)
            {
                LetterGrade = "D";
                Points = 1.0;
            }
            else
            {
                LetterGrade = "F";
                Points = 0.0;
            }
        }

        /// <summary>
        /// الحصول على النسبة المئوية للدرجة
        /// </summary>
        /// <returns>النسبة المئوية</returns>
        public double GetPercentage()
        {
            return MaxScore > 0 ? (Score / MaxScore) * 100 : 0;
        }

        /// <summary>
        /// التحقق من نجاح الطالب في المادة
        /// </summary>
        /// <returns>true إذا نجح، false إذا رسب</returns>
        public bool IsPassing()
        {
            return GetPercentage() >= 50;
        }

        public override string ToString()
        {
            return $"{SubjectName}: {Score}/{MaxScore} ({GetPercentage():F1}%) - {LetterGrade} ({Points} points)";
        }
    }
}
