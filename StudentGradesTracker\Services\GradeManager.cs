using System;
using System.Collections.Generic;
using System.Linq;
using StudentGradesTracker.Models;

namespace StudentGradesTracker.Services
{
    /// <summary>
    /// فئة إدارة الدرجات
    /// </summary>
    public class GradeManager
    {
        private StudentManager studentManager;
        private List<Subject> subjects;
        private int nextGradeId;
        private int nextSubjectId;

        public GradeManager(StudentManager studentManager)
        {
            this.studentManager = studentManager;
            subjects = new List<Subject>();
            nextGradeId = 1;
            nextSubjectId = 1;
            
            // إضافة مادة تقنية المعلومات كمادة افتراضية
            AddSubject("تقنية المعلومات", "IT101", 3, "د. أحمد محمد");
        }

        /// <summary>
        /// إضافة مادة جديدة
        /// </summary>
        /// <param name="name">اسم المادة</param>
        /// <param name="code">رمز المادة</param>
        /// <param name="creditHours">الساعات المعتمدة</param>
        /// <param name="instructor">المدرس</param>
        /// <returns>المادة المضافة</returns>
        public Subject AddSubject(string name, string code, int creditHours, string instructor)
        {
            var subject = new Subject(nextSubjectId++, name, code, creditHours, instructor);
            subjects.Add(subject);
            return subject;
        }

        /// <summary>
        /// الحصول على جميع المواد
        /// </summary>
        /// <returns>قائمة المواد</returns>
        public List<Subject> GetAllSubjects()
        {
            return new List<Subject>(subjects);
        }

        /// <summary>
        /// البحث عن مادة بالاسم
        /// </summary>
        /// <param name="name">اسم المادة</param>
        /// <returns>المادة أو null</returns>
        public Subject GetSubjectByName(string name)
        {
            return subjects.FirstOrDefault(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// إضافة درجة لطالب في مادة معينة
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        /// <param name="subjectName">اسم المادة</param>
        /// <param name="score">الدرجة</param>
        /// <param name="maxScore">الدرجة القصوى</param>
        /// <param name="semester">الفصل الدراسي</param>
        /// <returns>true إذا تم إضافة الدرجة بنجاح</returns>
        public bool AddGrade(int studentId, string subjectName, double score, double maxScore = 100.0, string semester = "")
        {
            var student = studentManager.GetStudentById(studentId);
            var subject = GetSubjectByName(subjectName);

            if (student == null || subject == null)
                return false;

            if (string.IsNullOrEmpty(semester))
                semester = GetCurrentSemester();

            var grade = new Grade(studentId, subjectName, score, maxScore, subject.CreditHours)
            {
                Id = nextGradeId++,
                Semester = semester
            };

            // إضافة الدرجة للطالب
            student.AddOrUpdateGrade(grade);
            
            // إضافة الدرجة للمادة
            var existingGrade = subject.Grades.FirstOrDefault(g => g.StudentId == studentId);
            if (existingGrade != null)
            {
                subject.Grades.Remove(existingGrade);
            }
            subject.Grades.Add(grade);

            return true;
        }

        /// <summary>
        /// تحديث درجة طالب
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        /// <param name="subjectName">اسم المادة</param>
        /// <param name="newScore">الدرجة الجديدة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateGrade(int studentId, string subjectName, double newScore)
        {
            var student = studentManager.GetStudentById(studentId);
            if (student == null)
                return false;

            var grade = student.GetGradeBySubject(subjectName);
            if (grade == null)
                return false;

            grade.Score = newScore;
            grade.CalculateLetterGradeAndPoints();
            grade.DateRecorded = DateTime.Now;

            // تحديث الدرجة في المادة أيضاً
            var subject = GetSubjectByName(subjectName);
            if (subject != null)
            {
                var subjectGrade = subject.Grades.FirstOrDefault(g => g.StudentId == studentId);
                if (subjectGrade != null)
                {
                    subjectGrade.Score = newScore;
                    subjectGrade.CalculateLetterGradeAndPoints();
                    subjectGrade.DateRecorded = DateTime.Now;
                }
            }

            return true;
        }

        /// <summary>
        /// حذف درجة طالب من مادة
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        /// <param name="subjectName">اسم المادة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteGrade(int studentId, string subjectName)
        {
            var student = studentManager.GetStudentById(studentId);
            if (student == null)
                return false;

            var grade = student.GetGradeBySubject(subjectName);
            if (grade == null)
                return false;

            student.Grades.Remove(grade);

            // حذف الدرجة من المادة أيضاً
            var subject = GetSubjectByName(subjectName);
            if (subject != null)
            {
                var subjectGrade = subject.Grades.FirstOrDefault(g => g.StudentId == studentId);
                if (subjectGrade != null)
                {
                    subject.Grades.Remove(subjectGrade);
                }
            }

            return true;
        }

        /// <summary>
        /// الحصول على درجات طالب في جميع المواد
        /// </summary>
        /// <param name="studentId">معرف الطالب</param>
        /// <returns>قائمة الدرجات</returns>
        public List<Grade> GetStudentGrades(int studentId)
        {
            var student = studentManager.GetStudentById(studentId);
            return student?.Grades ?? new List<Grade>();
        }

        /// <summary>
        /// الحصول على درجات جميع الطلاب في مادة معينة
        /// </summary>
        /// <param name="subjectName">اسم المادة</param>
        /// <returns>قائمة الدرجات</returns>
        public List<Grade> GetSubjectGrades(string subjectName)
        {
            var subject = GetSubjectByName(subjectName);
            return subject?.Grades ?? new List<Grade>();
        }

        /// <summary>
        /// الحصول على الفصل الدراسي الحالي
        /// </summary>
        /// <returns>الفصل الدراسي</returns>
        private string GetCurrentSemester()
        {
            int month = DateTime.Now.Month;
            if (month >= 9 || month <= 1)
                return "الفصل الأول";
            else if (month >= 2 && month <= 6)
                return "الفصل الثاني";
            else
                return "الفصل الصيفي";
        }

        /// <summary>
        /// حساب إحصائيات المادة
        /// </summary>
        /// <param name="subjectName">اسم المادة</param>
        /// <returns>إحصائيات المادة</returns>
        public Subject GetSubjectStatistics(string subjectName)
        {
            return GetSubjectByName(subjectName);
        }
    }
}
