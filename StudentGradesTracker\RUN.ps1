# Student Grades Tracker System - PowerShell Launcher
# Version 1.0 - 2024

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "Student Grades Tracker System"

Clear-Host

Write-Host ""
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "    Student Grades Tracker System" -ForegroundColor Cyan
Write-Host "    Version 1.0 - 2024" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Starting the system..." -ForegroundColor Yellow
Write-Host ""

# Get current script directory
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptPath

# Try to run the standalone executable first
$ExePath1 = Join-Path $ScriptPath "bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe"
$ExePath2 = Join-Path $ScriptPath "bin\Release\net6.0\win-x64\StudentGradesTracker.exe"

if (Test-Path $ExePath1) {
    Write-Host "Running standalone executable..." -ForegroundColor Green
    & $ExePath1
} elseif (Test-Path $ExePath2) {
    Write-Host "Running executable..." -ForegroundColor Green
    & $ExePath2
} else {
    Write-Host "Executable not found, trying dotnet run..." -ForegroundColor Yellow
    
    # Check if dotnet is available
    try {
        $dotnetVersion = dotnet --version
        Write-Host "Using .NET version: $dotnetVersion" -ForegroundColor Green
        
        # Run the application
        dotnet run
    }
    catch {
        Write-Host "ERROR: .NET is not installed or not found in PATH" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please install .NET 6.0 from:" -ForegroundColor Yellow
        Write-Host "https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Yellow
        Write-Host ""
    }
}

Write-Host ""
Write-Host "System ended." -ForegroundColor Green
Read-Host "Press Enter to exit"
