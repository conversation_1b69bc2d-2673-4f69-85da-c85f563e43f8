@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب

echo.
echo جاري إنشاء اختصار على سطح المكتب...

REM الحصول على مسار سطح المكتب
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

REM إنشاء ملف VBS لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\نظام رصد درجات الطلاب.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%~dp0تشغيل_النظام.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%~dp0" >> CreateShortcut.vbs
echo oLink.Description = "نظام رصد درجات الطلاب - مادة تقنية المعلومات" >> CreateShortcut.vbs
echo oLink.IconLocation = "%SystemRoot%\System32\shell32.dll,21" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

REM تشغيل ملف VBS
cscript CreateShortcut.vbs >nul

REM حذف ملف VBS المؤقت
del CreateShortcut.vbs

echo.
echo ✓ تم إنشاء اختصار "نظام رصد درجات الطلاب" على سطح المكتب بنجاح!
echo.
echo يمكنك الآن النقر المزدوج على الاختصار لتشغيل النظام.
echo.
pause
