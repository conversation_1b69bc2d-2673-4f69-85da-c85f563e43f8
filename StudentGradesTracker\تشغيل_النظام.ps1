# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "نظام رصد درجات الطلاب - مادة تقنية المعلومات"

Clear-Host

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "║        🎓 نظام رصد درجات الطلاب - مادة تقنية المعلومات        ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "║                    الإصدار 1.0 - 2024                      ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""
Write-Host "جاري تشغيل النظام..." -ForegroundColor Yellow
Write-Host ""

# الحصول على مسار الملف الحالي
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$ExePath = Join-Path $ScriptPath "bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe"

# محاولة تشغيل الملف التنفيذي
if (Test-Path $ExePath) {
    Write-Host "تشغيل الملف التنفيذي..." -ForegroundColor Green
    & $ExePath
} else {
    Write-Host "تعذر العثور على الملف التنفيذي، جاري المحاولة بـ dotnet..." -ForegroundColor Yellow
    Set-Location $ScriptPath
    dotnet run
}

Write-Host ""
Write-Host "تم إنهاء النظام." -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
