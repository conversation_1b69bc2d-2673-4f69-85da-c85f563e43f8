# 🎓 نظام رصد درجات الطلاب - مادة تقنية المعلومات

## وصف التطبيق

تطبيق شامل لرصد وإدارة درجات الطلاب في مادة تقنية المعلومات، مطور باستخدام C# و .NET 6. يوفر التطبيق **واجهة احترافية ملونة** باللغة العربية مع **التاريخ الميلادي** في وحدة التحكم لإدارة بيانات الطلاب ودرجاتهم مع إمكانيات متقدمة للتقارير والإحصائيات.

## 🌟 الواجهة الاحترافية الجديدة

- **🎨 تصميم احترافي**: واجهة ملونة مع رموز تعبيرية وجداول منسقة
- **📅 التاريخ الميلادي**: عرض التواريخ بالتقويم الميلادي
- **🌈 ألوان متنوعة**: ألوان مختلفة للنجاح والخطأ والمعلومات
- **📊 شريط التقدم**: شريط تحميل احترافي عند بدء التشغيل
- **📋 جداول منسقة**: عرض البيانات في جداول احترافية مع حدود
- **🔍 إحصائيات سريعة**: عرض نظرة سريعة على البيانات في كل صفحة

## الميزات الرئيسية

### 1. إدارة الطلاب
- إضافة طلاب جدد
- عرض قائمة جميع الطلاب
- البحث عن الطلاب (بالمعرف، رقم الطالب، أو الاسم)
- تعديل بيانات الطلاب
- حذف الطلاب

### 2. إدارة الدرجات
- إضافة درجات للطلاب
- تعديل الدرجات الموجودة
- حذف الدرجات
- عرض درجات طالب معين
- عرض درجات جميع الطلاب في مادة

### 3. التقارير والإحصائيات
- تقرير أفضل الطلاب
- تقرير الطلاب حسب المعدل
- تقرير الطلاب الناجحين والراسبين
- إحصائيات عامة للنظام
- إحصائيات مفصلة للمواد
- توزيع الدرجات الحرفية

### 4. إدارة البيانات
- حفظ البيانات تلقائياً في ملفات JSON
- تحميل البيانات عند بدء التطبيق
- إنشاء نسخ احتياطية
- تصدير البيانات إلى ملفات CSV

### 5. إنتاج التقارير
- تقرير شامل عن جميع الطلاب
- تقرير مفصل لمادة معينة
- تقرير فردي لكل طالب
- تقرير أفضل الطلاب
- حفظ التقارير في ملفات نصية

## نظام التقييم

يستخدم التطبيق نظام تقييم شامل:

### الدرجات الحرفية والنقاط
- A+ (90-100%): 4.0 نقاط
- A (85-89%): 3.7 نقاط
- B+ (80-84%): 3.3 نقاط
- B (75-79%): 3.0 نقاط
- C+ (70-74%): 2.7 نقاط
- C (65-69%): 2.3 نقاط
- D+ (60-64%): 2.0 نقاط
- D (50-59%): 1.0 نقاط
- F (أقل من 50%): 0.0 نقاط

### حساب المعدل
يتم حساب المعدل العام للطالب بناءً على:
- الدرجات المحققة في كل مادة
- عدد الساعات المعتمدة لكل مادة
- النقاط المكتسبة من كل درجة

## متطلبات التشغيل

- .NET 6.0 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- مساحة تخزين: 50 MB على الأقل

## طريقة التشغيل

### 1. تشغيل التطبيق
```bash
cd StudentGradesTracker
dotnet run
```

### 2. بناء التطبيق
```bash
dotnet build
```

### 3. إنشاء ملف تنفيذي
```bash
dotnet publish -c Release
```

## هيكل المشروع

```
StudentGradesTracker/
├── Models/                 # نماذج البيانات
│   ├── Student.cs         # فئة الطالب
│   ├── Grade.cs           # فئة الدرجة
│   └── Subject.cs         # فئة المادة
├── Services/              # خدمات النظام
│   ├── StudentManager.cs  # إدارة الطلاب
│   ├── GradeManager.cs    # إدارة الدرجات
│   ├── DataManager.cs     # إدارة البيانات
│   └── ReportGenerator.cs # إنتاج التقارير
├── UI/                    # واجهة المستخدم
│   └── ConsoleUI.cs       # واجهة وحدة التحكم
├── Data/                  # ملفات البيانات (يتم إنشاؤها تلقائياً)
├── Reports/               # التقارير المحفوظة (يتم إنشاؤها تلقائياً)
└── Program.cs             # نقطة دخول التطبيق
```

## ملفات البيانات

يحفظ التطبيق البيانات في مجلد `Data/`:
- `students.json`: بيانات الطلاب ودرجاتهم
- `subjects.json`: بيانات المواد الدراسية
- `Backup/`: النسخ الاحتياطية

## التقارير

يحفظ التطبيق التقارير في مجلد `Reports/`:
- تقارير الطلاب
- تقارير المواد
- تقارير الإحصائيات
- ملفات CSV للتصدير

## البيانات التجريبية

عند التشغيل الأول، يضيف التطبيق بيانات تجريبية تشمل:
- 5 طلاب بأسماء وأرقام مختلفة
- درجات في مادة تقنية المعلومات
- تنوع في الدرجات لإظهار جميع الميزات

## الاستخدام

### القائمة الرئيسية
1. **إدارة الطلاب**: إضافة، تعديل، حذف، والبحث عن الطلاب
2. **إدارة الدرجات**: إضافة وتعديل درجات الطلاب
3. **التقارير**: عرض تقارير مختلفة عن الطلاب والدرجات
4. **الإحصائيات**: عرض إحصائيات مفصلة
5. **إدارة البيانات**: حفظ، تحميل، وتصدير البيانات
6. **إنتاج التقارير**: إنشاء تقارير مفصلة وحفظها

### نصائح الاستخدام
- يتم حفظ البيانات تلقائياً عند الخروج من التطبيق
- يمكن إنشاء نسخ احتياطية في أي وقت
- التقارير يمكن حفظها كملفات نصية للمراجعة لاحقاً
- يدعم التطبيق النصوص العربية بالكامل

## المطور

تم تطوير هذا التطبيق كمشروع تعليمي لإدارة درجات الطلاب في مادة تقنية المعلومات.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.
