﻿using System;
using StudentGradesTracker.UI;
using StudentGradesTracker.Services;

namespace StudentGradesTracker
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.OutputEncoding = System.Text.Encoding.UTF8;
                Console.WriteLine("مرحباً بك في نظام رصد درجات الطلاب!");
                Console.WriteLine("جاري تحميل البيانات...");

                // إنشاء مدراء النظام
                var studentManager = new StudentManager();
                var gradeManager = new GradeManager(studentManager);
                var dataManager = new DataManager();

                // تحميل البيانات المحفوظة
                if (dataManager.DataFilesExist())
                {
                    if (dataManager.LoadAllData(studentManager, gradeManager))
                    {
                        Console.WriteLine("تم تحميل البيانات بنجاح!");
                    }
                    else
                    {
                        Console.WriteLine("تحذير: حدث خطأ في تحميل بعض البيانات.");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد بيانات محفوظة، سيتم البدء بنظام جديد.");

                    // إضافة بيانات تجريبية
                    AddSampleData(studentManager, gradeManager);
                }

                // تشغيل واجهة المستخدم
                var ui = new ConsoleUI(studentManager, gradeManager);
                ui.Run();

                // حفظ البيانات عند الخروج
                Console.WriteLine("جاري حفظ البيانات...");
                if (dataManager.SaveAllData(studentManager, gradeManager))
                {
                    Console.WriteLine("تم حفظ البيانات بنجاح!");
                }
                else
                {
                    Console.WriteLine("تحذير: حدث خطأ في حفظ البيانات.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"حدث خطأ في التطبيق: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// إضافة بيانات تجريبية للنظام
        /// </summary>
        /// <param name="studentManager">مدير الطلاب</param>
        /// <param name="gradeManager">مدير الدرجات</param>
        private static void AddSampleData(StudentManager studentManager, GradeManager gradeManager)
        {
            try
            {
                // إضافة طلاب تجريبيين
                var student1 = studentManager.AddStudent("أحمد محمد علي", "2021001", "<EMAIL>");
                var student2 = studentManager.AddStudent("فاطمة سالم أحمد", "2021002", "<EMAIL>");
                var student3 = studentManager.AddStudent("محمد عبدالله سعيد", "2021003", "<EMAIL>");
                var student4 = studentManager.AddStudent("نورا خالد محمد", "2021004", "<EMAIL>");
                var student5 = studentManager.AddStudent("عبدالرحمن يوسف", "2021005", "<EMAIL>");

                // إضافة درجات تجريبية في مادة تقنية المعلومات
                gradeManager.AddGrade(student1.Id, "تقنية المعلومات", 85, 100, "الفصل الأول");
                gradeManager.AddGrade(student2.Id, "تقنية المعلومات", 92, 100, "الفصل الأول");
                gradeManager.AddGrade(student3.Id, "تقنية المعلومات", 78, 100, "الفصل الأول");
                gradeManager.AddGrade(student4.Id, "تقنية المعلومات", 88, 100, "الفصل الأول");
                gradeManager.AddGrade(student5.Id, "تقنية المعلومات", 45, 100, "الفصل الأول");

                Console.WriteLine("تم إضافة بيانات تجريبية للنظام.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إضافة البيانات التجريبية: {ex.Message}");
            }
        }
    }
}
