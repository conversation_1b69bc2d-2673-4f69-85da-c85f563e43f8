@echo off
chcp 65001 >nul
title نظام رصد درجات الطلاب - التشغيل المحسن

color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🎓 نظام رصد درجات الطلاب - التشغيل المحسن            ║
echo ║                                                              ║
echo ║                    الإصدار 1.0 - 2024                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري فحص النظام والملفات...
echo.

REM التحقق من المجلد الحالي
echo 📁 المجلد الحالي: %CD%
echo.

REM طريقة 1: تشغيل الملف التنفيذي المستقل
set EXE_PATH="%~dp0bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe"
if exist %EXE_PATH% (
    echo ✅ تم العثور على الملف التنفيذي المستقل
    echo 🚀 جاري تشغيل النظام...
    echo.
    %EXE_PATH%
    goto :end
)

REM طريقة 2: تشغيل الملف التنفيذي العادي
set EXE_PATH2="%~dp0bin\Release\net6.0\win-x64\StudentGradesTracker.exe"
if exist %EXE_PATH2% (
    echo ✅ تم العثور على الملف التنفيذي العادي
    echo 🚀 جاري تشغيل النظام...
    echo.
    %EXE_PATH2%
    goto :end
)

REM طريقة 3: التحقق من dotnet وتشغيل المشروع
echo ⚠️  لم يتم العثور على الملف التنفيذي
echo 🔍 جاري البحث عن dotnet...

dotnet --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo 📥 لحل هذه المشكلة، يرجى:
    echo.
    echo 1️⃣  تحميل وتثبيت .NET 6.0 من:
    echo    https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo 2️⃣  أو تحميل .NET Runtime فقط من:
    echo    https://dotnet.microsoft.com/download/dotnet/6.0/runtime
    echo.
    echo 3️⃣  بعد التثبيت، أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET متوفر - الإصدار: %DOTNET_VERSION%

REM التحقق من ملفات المشروع
if not exist "StudentGradesTracker.csproj" (
    echo.
    echo ❌ ملفات المشروع غير موجودة في المجلد الحالي
    echo 📁 تأكد من أنك في المجلد الصحيح الذي يحتوي على:
    echo    - StudentGradesTracker.csproj
    echo    - Program.cs
    echo    - مجلد Models
    echo    - مجلد Services
    echo    - مجلد UI
    echo.
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

echo 🔨 جاري بناء المشروع...
dotnet build -c Release
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo 🔧 جاري المحاولة بتنظيف المشروع أولاً...
    dotnet clean
    dotnet restore
    dotnet build -c Release
    
    if errorlevel 1 (
        echo ❌ فشل في بناء المشروع مرة أخرى
        echo.
        echo 📞 يرجى التحقق من:
        echo    - اتصال الإنترنت
        echo    - صحة ملفات المشروع
        echo    - عدم وجود برامج مكافحة فيروسات تحجب العملية
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🚀 جاري تشغيل النظام...
echo.

dotnet run -c Release

:end
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo    1️⃣  تأكد من إغلاق أي نسخة أخرى من البرنامج
    echo    2️⃣  أعد تشغيل الكمبيوتر وحاول مرة أخرى
    echo    3️⃣  تأكد من وجود مساحة كافية على القرص الصلب
    echo    4️⃣  تحقق من إعدادات مكافح الفيروسات
    echo.
) else (
    echo.
    echo ✅ تم إنهاء النظام بنجاح
)

echo.
pause
