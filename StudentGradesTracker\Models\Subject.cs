using System;
using System.Collections.Generic;
using System.Linq;

namespace StudentGradesTracker.Models
{
    /// <summary>
    /// فئة تمثل المادة الدراسية
    /// </summary>
    public class Subject
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public int CreditHours { get; set; }
        public string Description { get; set; }
        public string Instructor { get; set; }
        public string Semester { get; set; }
        public int Year { get; set; }
        public List<Grade> Grades { get; set; }

        public Subject()
        {
            Grades = new List<Grade>();
            CreditHours = 3;
            Year = DateTime.Now.Year;
        }

        public Subject(int id, string name, string code, int creditHours, string instructor)
        {
            Id = id;
            Name = name;
            Code = code;
            CreditHours = creditHours;
            Instructor = instructor;
            Year = DateTime.Now.Year;
            Grades = new List<Grade>();
        }

        /// <summary>
        /// حساب متوسط الدرجات في المادة
        /// </summary>
        /// <returns>متوسط الدرجات</returns>
        public double CalculateAverageScore()
        {
            if (Grades.Count == 0)
                return 0.0;

            return Grades.Average(g => g.GetPercentage());
        }

        /// <summary>
        /// الحصول على أعلى درجة في المادة
        /// </summary>
        /// <returns>أعلى درجة</returns>
        public double GetHighestScore()
        {
            if (Grades.Count == 0)
                return 0.0;

            return Grades.Max(g => g.GetPercentage());
        }

        /// <summary>
        /// الحصول على أقل درجة في المادة
        /// </summary>
        /// <returns>أقل درجة</returns>
        public double GetLowestScore()
        {
            if (Grades.Count == 0)
                return 0.0;

            return Grades.Min(g => g.GetPercentage());
        }

        /// <summary>
        /// حساب عدد الطلاب الناجحين
        /// </summary>
        /// <returns>عدد الطلاب الناجحين</returns>
        public int GetPassingStudentsCount()
        {
            return Grades.Count(g => g.IsPassing());
        }

        /// <summary>
        /// حساب عدد الطلاب الراسبين
        /// </summary>
        /// <returns>عدد الطلاب الراسبين</returns>
        public int GetFailingStudentsCount()
        {
            return Grades.Count(g => !g.IsPassing());
        }

        /// <summary>
        /// حساب نسبة النجاح في المادة
        /// </summary>
        /// <returns>نسبة النجاح</returns>
        public double GetPassingRate()
        {
            if (Grades.Count == 0)
                return 0.0;

            return (double)GetPassingStudentsCount() / Grades.Count * 100;
        }

        /// <summary>
        /// الحصول على توزيع الدرجات الحرفية
        /// </summary>
        /// <returns>قاموس يحتوي على عدد كل درجة حرفية</returns>
        public Dictionary<string, int> GetGradeDistribution()
        {
            var distribution = new Dictionary<string, int>
            {
                {"A+", 0}, {"A", 0}, {"B+", 0}, {"B", 0}, 
                {"C+", 0}, {"C", 0}, {"D+", 0}, {"D", 0}, {"F", 0}
            };

            foreach (var grade in Grades)
            {
                if (distribution.ContainsKey(grade.LetterGrade))
                {
                    distribution[grade.LetterGrade]++;
                }
            }

            return distribution;
        }

        public override string ToString()
        {
            return $"{Code} - {Name} ({CreditHours} ساعات معتمدة) - المدرس: {Instructor}";
        }
    }
}
