@echo off
cls
title Student Grades System

echo Starting Student Grades Tracker...
echo.

cd /d "%~dp0"

if exist "bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe" (
    echo Running executable file...
    "bin\Release\net6.0\win-x64\publish\StudentGradesTracker.exe"
    goto end
)

if exist "bin\Release\net6.0\win-x64\StudentGradesTracker.exe" (
    echo Running executable file...
    "bin\Release\net6.0\win-x64\StudentGradesTracker.exe"
    goto end
)

echo Executable not found, trying dotnet run...
dotnet run

:end
echo.
echo Press any key to exit...
pause >nul
