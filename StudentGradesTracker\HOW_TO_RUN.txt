===============================================
    HOW TO RUN STUDENT GRADES TRACKER SYSTEM
===============================================

PROBLEM SOLVED! The application is working perfectly!

EASY SOLUTIONS (Choose one):

1. SIMPLEST WAY:
   - Double-click on: RUN.bat
   - This will automatically check your system and run the app

2. ALTERNATIVE WAY:
   - Double-click on: START.bat
   - This tries different methods to run the app

3. POWERSHELL WAY:
   - Right-click on: RUN.ps1
   - Select "Run with PowerShell"

4. MANUAL WAY:
   - Open Command Prompt
   - Navigate to the project folder
   - Type: dotnet run

REQUIREMENTS:
- .NET 6.0 or later must be installed
- Download from: https://dotnet.microsoft.com/download/dotnet/6.0

WHAT YOU'LL SEE:
- Beautiful Arabic interface with colors
- Professional loading screen
- Main menu with 6 options:
  [1] Student Management
  [2] Grades Management  
  [3] Quick Reports
  [4] Detailed Statistics
  [5] Data Management
  [6] Report Generation

TROUBLESHOOTING:
- If you see encoding errors in batch files, use RUN.bat instead
- If .NET is not installed, download it from the link above
- If Windows Defender blocks the app, add an exception

FILES CREATED FOR YOU:
- RUN.bat (RECOMMENDED - Use this one!)
- START.bat (Alternative)
- RUN.ps1 (PowerShell version)
- HOW_TO_RUN.txt (This file)

The application is fully functional with:
✓ Professional Arabic interface
✓ Gregorian calendar dates
✓ Colorful design with emojis
✓ Progress bars and animations
✓ Complete student and grades management

ENJOY USING THE SYSTEM!
