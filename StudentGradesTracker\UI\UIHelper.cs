using System;
using System.Globalization;

namespace StudentGradesTracker.UI
{
    /// <summary>
    /// فئة مساعدة لتحسين واجهة المستخدم
    /// </summary>
    public static class UIHelper
    {
        // الألوان
        public static readonly ConsoleColor HeaderColor = ConsoleColor.Cyan;
        public static readonly ConsoleColor MenuColor = ConsoleColor.Yellow;
        public static readonly ConsoleColor SuccessColor = ConsoleColor.Green;
        public static readonly ConsoleColor ErrorColor = ConsoleColor.Red;
        public static readonly ConsoleColor InfoColor = ConsoleColor.Blue;
        public static readonly ConsoleColor WarningColor = ConsoleColor.DarkYellow;

        /// <summary>
        /// طباعة عنوان رئيسي
        /// </summary>
        /// <param name="title">النص</param>
        public static void PrintHeader(string title)
        {
            Console.Clear();
            Console.ForegroundColor = HeaderColor;
            
            string border = new string('═', title.Length + 8);
            Console.WriteLine($"╔{border}╗");
            Console.WriteLine($"║    {title}    ║");
            Console.WriteLine($"╚{border}╝");
            
            Console.ResetColor();
            Console.WriteLine();
        }

        /// <summary>
        /// طباعة عنوان فرعي
        /// </summary>
        /// <param name="title">النص</param>
        public static void PrintSubHeader(string title)
        {
            Console.ForegroundColor = MenuColor;
            string border = new string('─', title.Length + 4);
            Console.WriteLine($"┌{border}┐");
            Console.WriteLine($"│  {title}  │");
            Console.WriteLine($"└{border}┘");
            Console.ResetColor();
            Console.WriteLine();
        }

        /// <summary>
        /// طباعة خيار في القائمة
        /// </summary>
        /// <param name="number">رقم الخيار</param>
        /// <param name="text">نص الخيار</param>
        public static void PrintMenuItem(string number, string text)
        {
            Console.ForegroundColor = MenuColor;
            Console.Write($"[{number}] ");
            Console.ResetColor();
            Console.WriteLine(text);
        }

        /// <summary>
        /// طباعة رسالة نجاح
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void PrintSuccess(string message)
        {
            Console.ForegroundColor = SuccessColor;
            Console.WriteLine($"✓ {message}");
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة رسالة خطأ
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void PrintError(string message)
        {
            Console.ForegroundColor = ErrorColor;
            Console.WriteLine($"✗ {message}");
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة رسالة معلومات
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void PrintInfo(string message)
        {
            Console.ForegroundColor = InfoColor;
            Console.WriteLine($"ℹ {message}");
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة رسالة تحذير
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void PrintWarning(string message)
        {
            Console.ForegroundColor = WarningColor;
            Console.WriteLine($"⚠ {message}");
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة جدول
        /// </summary>
        /// <param name="headers">عناوين الأعمدة</param>
        /// <param name="widths">عرض كل عمود</param>
        public static void PrintTableHeader(string[] headers, int[] widths)
        {
            Console.ForegroundColor = HeaderColor;
            
            // الخط العلوي
            Console.Write("┌");
            for (int i = 0; i < headers.Length; i++)
            {
                Console.Write(new string('─', widths[i] + 2));
                if (i < headers.Length - 1) Console.Write("┬");
            }
            Console.WriteLine("┐");

            // العناوين
            Console.Write("│");
            for (int i = 0; i < headers.Length; i++)
            {
                Console.Write($" {headers[i].PadRight(widths[i])} ");
                if (i < headers.Length - 1) Console.Write("│");
            }
            Console.WriteLine("│");

            // الخط الفاصل
            Console.Write("├");
            for (int i = 0; i < headers.Length; i++)
            {
                Console.Write(new string('─', widths[i] + 2));
                if (i < headers.Length - 1) Console.Write("┼");
            }
            Console.WriteLine("┤");
            
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة صف في الجدول
        /// </summary>
        /// <param name="values">القيم</param>
        /// <param name="widths">عرض كل عمود</param>
        public static void PrintTableRow(string[] values, int[] widths)
        {
            Console.Write("│");
            for (int i = 0; i < values.Length; i++)
            {
                Console.Write($" {values[i].PadRight(widths[i])} ");
                if (i < values.Length - 1) Console.Write("│");
            }
            Console.WriteLine("│");
        }

        /// <summary>
        /// طباعة نهاية الجدول
        /// </summary>
        /// <param name="widths">عرض الأعمدة</param>
        public static void PrintTableFooter(int[] widths)
        {
            Console.ForegroundColor = HeaderColor;
            Console.Write("└");
            for (int i = 0; i < widths.Length; i++)
            {
                Console.Write(new string('─', widths[i] + 2));
                if (i < widths.Length - 1) Console.Write("┴");
            }
            Console.WriteLine("┘");
            Console.ResetColor();
        }

        /// <summary>
        /// قراءة إدخال من المستخدم مع رسالة
        /// </summary>
        /// <param name="prompt">الرسالة</param>
        /// <returns>الإدخال</returns>
        public static string GetInput(string prompt)
        {
            Console.ForegroundColor = InfoColor;
            Console.Write($"➤ {prompt}: ");
            Console.ResetColor();
            return Console.ReadLine() ?? "";
        }

        /// <summary>
        /// انتظار ضغط مفتاح
        /// </summary>
        public static void WaitForKey()
        {
            Console.ForegroundColor = ConsoleColor.Gray;
            Console.WriteLine("\n⏎ اضغط أي مفتاح للمتابعة...");
            Console.ResetColor();
            Console.ReadKey();
        }

        /// <summary>
        /// تنسيق التاريخ الميلادي
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>التاريخ منسق</returns>
        public static string FormatDate(DateTime date)
        {
            var culture = new CultureInfo("ar-SA");
            culture.DateTimeFormat.Calendar = new GregorianCalendar();
            return date.ToString("dd/MM/yyyy", culture);
        }

        /// <summary>
        /// تنسيق التاريخ والوقت الميلادي
        /// </summary>
        /// <param name="dateTime">التاريخ والوقت</param>
        /// <returns>التاريخ والوقت منسق</returns>
        public static string FormatDateTime(DateTime dateTime)
        {
            var culture = new CultureInfo("ar-SA");
            culture.DateTimeFormat.Calendar = new GregorianCalendar();
            return dateTime.ToString("dd/MM/yyyy HH:mm", culture);
        }

        /// <summary>
        /// طباعة شريط التقدم
        /// </summary>
        /// <param name="current">القيمة الحالية</param>
        /// <param name="total">القيمة الإجمالية</param>
        /// <param name="width">عرض الشريط</param>
        public static void PrintProgressBar(int current, int total, int width = 30)
        {
            double percentage = (double)current / total;
            int filled = (int)(percentage * width);
            
            Console.Write("[");
            Console.ForegroundColor = SuccessColor;
            Console.Write(new string('█', filled));
            Console.ResetColor();
            Console.Write(new string('░', width - filled));
            Console.WriteLine($"] {percentage:P0}");
        }

        /// <summary>
        /// طباعة إحصائية مع أيقونة
        /// </summary>
        /// <param name="icon">الأيقونة</param>
        /// <param name="label">التسمية</param>
        /// <param name="value">القيمة</param>
        /// <param name="color">اللون</param>
        public static void PrintStat(string icon, string label, string value, ConsoleColor color = ConsoleColor.White)
        {
            Console.ForegroundColor = color;
            Console.Write($"{icon} ");
            Console.ResetColor();
            Console.Write($"{label}: ");
            Console.ForegroundColor = color;
            Console.WriteLine(value);
            Console.ResetColor();
        }

        /// <summary>
        /// طباعة فاصل
        /// </summary>
        /// <param name="length">الطول</param>
        public static void PrintSeparator(int length = 60)
        {
            Console.ForegroundColor = ConsoleColor.DarkGray;
            Console.WriteLine(new string('─', length));
            Console.ResetColor();
        }
    }
}
